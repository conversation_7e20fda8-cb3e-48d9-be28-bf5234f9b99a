/*!
    \file    gd32f4xx_it.c
    \brief   interrupt service routines
    
    \version 2020-09-04, V2.0.0, demo for GD32F4xx
*/

/*
    Copyright (c) 2020, GigaDevice Semiconductor Inc.

    Redistribution and use in source and binary forms, with or without modification, 
are permitted provided that the following conditions are met:

    1. Redistributions of source code must retain the above copyright notice, this 
       list of conditions and the following disclaimer.
    2. Redistributions in binary form must reproduce the above copyright notice, 
       this list of conditions and the following disclaimer in the documentation 
       and/or other materials provided with the distribution.
    3. Neither the name of the copyright holder nor the names of its contributors 
       may be used to endorse or promote products derived from this software without 
       specific prior written permission.

    THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" 
AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED 
WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. 
IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, 
INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT 
NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR 
PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, 
WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) 
ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY 
OF SUCH DAMAGE.
*/

#include "gd32f4xx_it.h"
#include "systick.h"
#include "Function.h"

/*!
    \brief      this function handles NMI exception
    \param[in]  none
    \param[out] none
    \retval     none
*/
void NMI_Handler(void)
{
}

/*!
    \brief      this function handles HardFault exception
    \param[in]  none
    \param[out] none
    \retval     none
*/
void HardFault_Handler(void)
{
    /* if Hard Fault exception occurs, go to infinite loop */
    while (1){
    }
}

/*!
    \brief      this function handles MemManage exception
    \param[in]  none
    \param[out] none
    \retval     none
*/
void MemManage_Handler(void)
{
    /* if Memory Manage exception occurs, go to infinite loop */
    while (1){
    }
}

/*!
    \brief      this function handles BusFault exception
    \param[in]  none
    \param[out] none
    \retval     none
*/
void BusFault_Handler(void)
{
    /* if Bus Fault exception occurs, go to infinite loop */
    while (1){
    }
}

/*!
    \brief      this function handles UsageFault exception
    \param[in]  none
    \param[out] none
    \retval     none
*/
void UsageFault_Handler(void)
{
    /* if Usage Fault exception occurs, go to infinite loop */
    while (1){
    }
}

/*!
    \brief      this function handles SVC exception
    \param[in]  none
    \param[out] none
    \retval     none
*/
void SVC_Handler(void)
{
}

/*!
    \brief      this function handles DebugMon exception
    \param[in]  none
    \param[out] none
    \retval     none
*/
void DebugMon_Handler(void)
{
}

/*!
    \brief      this function handles PendSV exception
    \param[in]  none
    \param[out] none
    \retval     none
*/
void PendSV_Handler(void)
{
}

/*!
    \brief      this function handles SysTick exception
    \param[in]  none
    \param[out] none
    \retval     none
*/
void SysTick_Handler(void)
{
    delay_decrement();
}

// 串口接收缓冲区
static char uart_rx_buffer[CMD_BUFFER_SIZE];
static uint8_t uart_rx_index = 0;

/*!
    \brief      USART0中断处理函数
    \param[in]  none
    \param[out] none
    \retval     none
*/
void USART0_IRQHandler(void)
{
    extern uint8_t input_mode;
    extern char input_buffer[20];
    extern uint8_t input_index;

    if(RESET != usart_interrupt_flag_get(USART0, USART_INT_FLAG_RBNE)) {
        uint8_t received_char = usart_data_receive(USART0);

        if(received_char == '\r' || received_char == '\n') {
            if(input_mode == 0) {  // 普通命令模式
                if(uart_rx_index > 0) {
                    uart_rx_buffer[uart_rx_index] = '\0';  // 字符串结束符
                    process_uart_command(uart_rx_buffer);  // 处理命令
                    uart_rx_index = 0;  // 重置缓冲区索引
                }
            } else {  // 参数输入模式
                if(input_index > 0) {
                    input_buffer[input_index] = '\0';
                    if(input_mode == 1) {  // ratio输入
                        float value = atof(input_buffer);
                        if(validate_ratio(value)) {
                            g_config_params.ratio = value;
                            printf("ratio modified success\r\n");
                            printf("Ratio = %.1f\r\n", g_config_params.ratio);
                        } else {
                            printf("ratio invalid\r\n");
                            printf("Ratio = %.1f\r\n", g_config_params.ratio);
                        }
                    } else if(input_mode == 2) {  // limit输入
                        float value = atof(input_buffer);
                        if(validate_limit(value)) {
                            g_config_params.limit = value;
                            printf("limit modified success\r\n");
                            printf("limit = %.2f\r\n", g_config_params.limit);
                        } else {
                            printf("limit invalid\r\n");
                            printf("limit = %.2f\r\n", g_config_params.limit);
                        }
                    }
                    input_mode = 0;  // 退出输入模式
                    input_index = 0;
                }
            }
        } else {
            if(input_mode == 0) {  // 普通命令模式
                if(uart_rx_index < CMD_BUFFER_SIZE - 1) {
                    uart_rx_buffer[uart_rx_index++] = received_char;
                }
            } else {  // 参数输入模式
                if(input_index < 19) {
                    input_buffer[input_index++] = received_char;
                }
            }
        }

        usart_interrupt_flag_clear(USART0, USART_INT_FLAG_RBNE);
    }
}
