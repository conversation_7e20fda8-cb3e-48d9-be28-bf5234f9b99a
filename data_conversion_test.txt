数据转换测试示例

测试用例1：
时间：2025-01-01 12:30:45
电压：12.5V
预期结果：6774C4F5000C8000

计算过程：
1. 时间戳转换：
   2025-01-01 12:30:45 → Unix时间戳 1735705845 → 16进制 6774C4F5

2. 电压转换：
   12.5V → 整数部分 12 → 16进制 000C
   12.5V → 小数部分 0.5 × 65536 = 32768 → 16进制 8000
   合并：000C8000

3. 最终结果：6774C4F5000C8000

测试用例2（超限）：
时间：2025-01-01 12:30:50
电压：15.2V（假设limit=10.0）
预期结果：6774C4FA000F3333*

计算过程：
1. 时间戳：1735705850 → 6774C4FA
2. 电压：15.2V → 000F + 0.2×65536=13107(3333) → 000F3333
3. 超限标记：*
4. 最终结果：6774C4FA000F3333*

解码验证：
6774C4F5000C8000 →
- 时间戳：6774C4F5 → 1735705845 → 2025-01-01 12:30:45
- 电压：000C8000 → 整数12 + 小数32768/65536=0.5 → 12.5V
