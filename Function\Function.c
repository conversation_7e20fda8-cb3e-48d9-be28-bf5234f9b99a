#include "Function.h"

int adc_value;  
float Vol_Value;  

void System_Init(void)
{
	systick_config();
    LED_Init();
    timer_config();
    USART0_Config();
	rcu_periph_clock_enable(RCU_GPIOC);
	gpio_mode_set(GPIOC, GPIO_MODE_ANALOG, GPIO_PUPD_NONE, GPIO_PIN_0);
	rcu_periph_clock_enable(RCU_ADC0);
	adc_clock_config(ADC_ADCCK_PCLK2_DIV8);
	ADC_Init();
	adc_software_trigger_enable(ADC0, ADC_ROUTINE_CHANNEL);
	OLED_Init();
	delay_1ms(10);
    nvic_irq_enable(USART0_IRQn, 0, 0);//使能USART0中断

	usart_interrupt_enable(USART0, USART_INT_RBNE);//接收中断打开

	// 初始化SPI Flash
	spi_flash_init();

	// 初始化RTC
	RTC_Init();
}

void UsrFunction(void)
{
    printf("CIMC Sys Init\r\n");
    char data[100];
	while(1)
	{
        led_change();
		OLED_ShowString(0,0,"**Hello CIMC**",16);
        delay_1ms(100);
		adc_flag_clear(ADC0,ADC_FLAG_EOC);
		while(SET != adc_flag_get(ADC0,ADC_FLAG_EOC)){}
        adc_value = ADC_RDATA(ADC0);
		Vol_Value = adc_value*3.3/4095;
		sprintf(data,"Vol=%.2f V",Vol_Value);
		OLED_ShowString(0,16,(uint8_t*)data,16);
		delay_1ms(100);
		OLED_Refresh();
	}
}

/*!
    \brief      处理串口命令
    \param[in]  cmd: 接收到的命令字符串
    \param[out] none
    \retval     none
*/
void process_uart_command(char* cmd)
{
    if(strcmp(cmd, "test") == 0) {
        system_selftest();
    } else if(strncmp(cmd, "RTC Config", 10) == 0) {
        // 提取时间字符串（跳过"RTC Config "）
        char* time_str = cmd + 11;
        rtc_config_command(time_str);
    } else if(strcmp(cmd, "RTC now") == 0) {
        rtc_now_command();
    }
}

/*!
    \brief      系统自检功能
    \param[in]  none
    \param[out] none
    \retval     none
*/
void system_selftest(void)
{
    printf("======system selftest======\r\n");

    // 检测Flash
    uint32_t flash_id = spi_flash_read_id();
    if(flash_id != 0 && flash_id != 0xFFFFFF) {
        printf("fflash............ok\r\n");
        printf("flash ID:0x%06X\r\n", flash_id);
    } else {
        printf("fflash............error\r\n");
        printf("can not read flash ID\r\n");
    }

    // 检测TF卡
    sd_error_enum sd_status = sd_init();
    if(sd_status == SD_OK) {
        printf("TFcard.......ok\r\n");
        uint32_t capacity = sd_card_capacity_get();
        printf("TF card memory:%dKB\r\n", capacity);
    } else {
        printf("TFcard.......error\r\n");
        printf("can not find TFcard\r\n");
    }

    // 显示RTC时间
    rtc_current_time_get(&rtc_initpara);
    printf("RTC:20%02d-%02d-%02d %02d:%02d:%02d\r\n",
           rtc_initpara.year, rtc_initpara.month, rtc_initpara.date,
           rtc_initpara.hour, rtc_initpara.minute, rtc_initpara.second);

    printf("======system selftest======\r\n");
}

/*!
    \brief      RTC配置命令处理
    \param[in]  time_str: 时间字符串
    \param[out] none
    \retval     none
*/
void rtc_config_command(char* time_str)
{
    uint32_t year, month, day, hour, minute, second;

    if(parse_time_string(time_str, &year, &month, &day, &hour, &minute, &second)) {
        // 配置RTC时间
        rtc_initpara.year = year;
        rtc_initpara.month = month;
        rtc_initpara.date = day;
        rtc_initpara.hour = hour;
        rtc_initpara.minute = minute;
        rtc_initpara.second = second;
        rtc_initpara.factor_asyn = prescaler_a;
        rtc_initpara.factor_syn = prescaler_s;
        rtc_initpara.display_format = RTC_24HOUR;
        rtc_initpara.am_pm = RTC_AM;

        if(ERROR == rtc_init(&rtc_initpara)) {
            printf("RTC Config failed\r\n");
        } else {
            printf("RTC Config success\r\n");
            printf("Time:20%02d-%02d-%02d %02d:%02d:%02d\r\n",
                   year, month, day, hour, minute, second);
        }
    } else {
        printf("Invalid time format\r\n");
    }
}

/*!
    \brief      RTC当前时间命令处理
    \param[in]  none
    \param[out] none
    \retval     none
*/
void rtc_now_command(void)
{
    rtc_current_time_get(&rtc_initpara);
    printf("Current Time: 20%02d-%02d-%02d %02d:%02d:%02d\r\n",
           rtc_initpara.year, rtc_initpara.month, rtc_initpara.date,
           rtc_initpara.hour, rtc_initpara.minute, rtc_initpara.second);
}

/*!
    \brief      解析时间字符串
    \param[in]  time_str: 时间字符串
    \param[out] year, month, day, hour, minute, second: 解析出的时间值
    \retval     1: 解析成功, 0: 解析失败
*/
uint8_t parse_time_string(char* time_str, uint32_t* year, uint32_t* month, uint32_t* day,
                         uint32_t* hour, uint32_t* minute, uint32_t* second)
{
    // 支持多种时间格式：2025-01-01 12:00:30 或 2025 01 01 12 00 30
    int parsed = sscanf(time_str, "%d-%d-%d %d:%d:%d", year, month, day, hour, minute, second);
    if(parsed != 6) {
        parsed = sscanf(time_str, "%d %d %d %d %d %d", year, month, day, hour, minute, second);
    }
    if(parsed != 6) {
        parsed = sscanf(time_str, "%d-%d-%d %d-%d-%d", year, month, day, hour, minute, second);
    }

    if(parsed == 6) {
        // 转换为BCD格式
        *year = (*year % 100);  // 只取年份的后两位
        *year = ((*year / 10) << 4) | (*year % 10);
        *month = ((*month / 10) << 4) | (*month % 10);
        *day = ((*day / 10) << 4) | (*day % 10);
        *hour = ((*hour / 10) << 4) | (*hour % 10);
        *minute = ((*minute / 10) << 4) | (*minute % 10);
        *second = ((*second / 10) << 4) | (*second % 10);
        return 1;
    }
    return 0;
}




