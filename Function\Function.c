#include "Function.h"

int adc_value;
float Vol_Value;

// ȫ�ֱ���
config_params_t g_config_params = {1.0f, 1.0f}; // Ĭ��ֵ
FATFS g_fatfs;  // FatFS�ļ�ϵͳ����
static uint8_t input_mode = 0;  // ����ģʽ��־ 0=����ģʽ 1=ratio���� 2=limit����
static char input_buffer[20];   // ��������
static uint8_t input_index = 0; // ���������

// ��������ȫ�ֱ���
uint8_t g_sampling_active = 0;      // ����״̬��־
uint8_t g_led1_state = 0;           // LED1״̬
uint32_t g_sampling_counter = 0;    // ��������
uint32_t g_led_counter = 0;         // LED��������
uint32_t g_system_tick = 0;         // ϵͳ��ʱ������
sampling_config_t g_sampling_config = {5}; // Ĭ��5������
uint8_t g_hide_mode = 0;                // ���ݶ���ģʽ��־ 0=����ģʽ 1=����ģʽ

// ���ݴ洢ȫ�ֱ���
data_storage_t g_data_storage = {0};

void System_Init(void)
{
	systick_config();
    LED_Init();
    timer_config();
    USART0_Config();
	rcu_periph_clock_enable(RCU_GPIOC);
	gpio_mode_set(GPIOC, GPIO_MODE_ANALOG, GPIO_PUPD_NONE, GPIO_PIN_0);
	rcu_periph_clock_enable(RCU_ADC0);
	adc_clock_config(ADC_ADCCK_PCLK2_DIV8);
	ADC_Init();
	adc_software_trigger_enable(ADC0, ADC_ROUTINE_CHANNEL);
	OLED_Init();
	delay_1ms(10);
    nvic_irq_enable(USART0_IRQn, 0, 0);//使能USART0中断

	usart_interrupt_enable(USART0, USART_INT_RBNE);//接收中断打开

	// 初始化SPI Flash
	spi_flash_init();

	// 初始化RTC
	RTC_Init();

	// 挂载文件系统
	f_mount(0, &g_fatfs);

	// 从Flash加载配置参数
	load_config_from_flash();

	// 加载采样配置
	load_sampling_config();

	// 初始化OLED显示
	OLED_ShowString(0,0,"system idle",16);
	OLED_ShowString(0,16,"",16);
	OLED_Refresh();

	// 初始化数据存储系统
	data_storage_init();
}

void UsrFunction(void)
{
    printf("CIMC Sys Init\r\n");
	while(1)
	{
        key_scan_process();     // 按键扫描处理
        sampling_process();     // 采样处理
        update_oled_display();  // 更新OLED显示
        delay_1ms(10);          // 短延时
	}
}

/*!
    \brief      处理串口命令
    \param[in]  cmd: 接收到的命令字符串
    \param[out] none
    \retval     none
*/
void process_uart_command(char* cmd)
{
    if(strcmp(cmd, "test") == 0) {
        system_selftest();
    } else if(strncmp(cmd, "RTC Config", 10) == 0) {
        // 提取时间字符串（跳过"RTC Config "）
        char* time_str = cmd + 11;
        rtc_config_command(time_str);
    } else if(strcmp(cmd, "RTC now") == 0) {
        rtc_now_command();
    } else if(strcmp(cmd, "conf") == 0) {
        config_read_command();
    } else if(strcmp(cmd, "ratio") == 0) {
        ratio_set_command();
    } else if(strcmp(cmd, "limit") == 0) {
        limit_set_command();
    } else if(strcmp(cmd, "config save") == 0) {
        config_save_command();
    } else if(strcmp(cmd, "config read") == 0) {
        config_read_flash_command();
    } else if(strcmp(cmd, "start") == 0) {
        sampling_start_command();
    } else if(strcmp(cmd, "stop") == 0) {
        sampling_stop_command();
    } else if(strcmp(cmd, "hide") == 0) {
        hide_data_command();
    } else if(strcmp(cmd, "unhide") == 0) {
        unhide_data_command();
    }
}

/*!
    \brief      系统自检功能
    \param[in]  none
    \param[out] none
    \retval     none
*/
void system_selftest(void)
{
    printf("======system selftest======\r\n");

    // 检测Flash
    uint32_t flash_id = spi_flash_read_id();
    if(flash_id != 0 && flash_id != 0xFFFFFF) {
        printf("fflash............ok\r\n");
        printf("flash ID:0x%06X\r\n", flash_id);
    } else {
        printf("fflash............error\r\n");
        printf("can not read flash ID\r\n");
    }

    // 检测TF卡
    sd_error_enum sd_status = sd_init();
    if(sd_status == SD_OK) {
        printf("TFcard.......ok\r\n");
        uint32_t capacity = sd_card_capacity_get();
        printf("TF card memory:%dKB\r\n", capacity);
    } else {
        printf("TFcard.......error\r\n");
        printf("can not find TFcard\r\n");
    }

    // 显示RTC时间
    rtc_current_time_get(&rtc_initpara);
    printf("RTC:20%02d-%02d-%02d %02d:%02d:%02d\r\n",
           rtc_initpara.year, rtc_initpara.month, rtc_initpara.date,
           rtc_initpara.hour, rtc_initpara.minute, rtc_initpara.second);

    printf("======system selftest======\r\n");
}

/*!
    \brief      RTC配置命令处理
    \param[in]  time_str: 时间字符串
    \param[out] none
    \retval     none
*/
void rtc_config_command(char* time_str)
{
    uint32_t year, month, day, hour, minute, second;

    if(parse_time_string(time_str, &year, &month, &day, &hour, &minute, &second)) {
        // 配置RTC时间
        rtc_initpara.year = year;
        rtc_initpara.month = month;
        rtc_initpara.date = day;
        rtc_initpara.hour = hour;
        rtc_initpara.minute = minute;
        rtc_initpara.second = second;
        rtc_initpara.factor_asyn = prescaler_a;
        rtc_initpara.factor_syn = prescaler_s;
        rtc_initpara.display_format = RTC_24HOUR;
        rtc_initpara.am_pm = RTC_AM;

        if(ERROR == rtc_init(&rtc_initpara)) {
            printf("RTC Config failed\r\n");
        } else {
            printf("RTC Config success\r\n");
            printf("Time:20%02d-%02d-%02d %02d:%02d:%02d\r\n",
                   year, month, day, hour, minute, second);
        }
    } else {
        printf("Invalid time format\r\n");
    }
}

/*!
    \brief      RTC当前时间命令处理
    \param[in]  none
    \param[out] none
    \retval     none
*/
void rtc_now_command(void)
{
    rtc_current_time_get(&rtc_initpara);
    printf("Current Time: 20%02d-%02d-%02d %02d:%02d:%02d\r\n",
           rtc_initpara.year, rtc_initpara.month, rtc_initpara.date,
           rtc_initpara.hour, rtc_initpara.minute, rtc_initpara.second);
}

/*!
    \brief      解析时间字符串
    \param[in]  time_str: 时间字符串
    \param[out] year, month, day, hour, minute, second: 解析出的时间值
    \retval     1: 解析成功, 0: 解析失败
*/
uint8_t parse_time_string(char* time_str, uint32_t* year, uint32_t* month, uint32_t* day,
                         uint32_t* hour, uint32_t* minute, uint32_t* second)
{
    // 支持多种时间格式：2025-01-01 12:00:30 或 2025 01 01 12 00 30
    int parsed = sscanf(time_str, "%d-%d-%d %d:%d:%d", year, month, day, hour, minute, second);
    if(parsed != 6) {
        parsed = sscanf(time_str, "%d %d %d %d %d %d", year, month, day, hour, minute, second);
    }
    if(parsed != 6) {
        parsed = sscanf(time_str, "%d-%d-%d %d-%d-%d", year, month, day, hour, minute, second);
    }

    if(parsed == 6) {
        // 转换为BCD格式
        *year = (*year % 100);  // 只取年份的后两位
        *year = ((*year / 10) << 4) | (*year % 10);
        *month = ((*month / 10) << 4) | (*month % 10);
        *day = ((*day / 10) << 4) | (*day % 10);
        *hour = ((*hour / 10) << 4) | (*hour % 10);
        *minute = ((*minute / 10) << 4) | (*minute % 10);
        *second = ((*second / 10) << 4) | (*second % 10);
        return 1;
    }
    return 0;
}

/*!
    \brief      读取配置文件命令
    \param[in]  none
    \param[out] none
    \retval     none
*/
void config_read_command(void)
{
    if(parse_config_file()) {
        printf("Ratio = %.1f\r\n", g_config_params.ratio);
        printf("Limit = %.2f\r\n", g_config_params.limit);
        printf("config read success\r\n");
    } else {
        printf("config.ini file not found.\r\n");
    }
}

/*!
    \brief      变比设置命令
    \param[in]  none
    \param[out] none
    \retval     none
*/
void ratio_set_command(void)
{
    extern uint8_t input_mode;
    extern uint8_t input_index;

    printf("Ratio=%.1f\r\n", g_config_params.ratio);
    printf("Input value(0~100):");
    input_mode = 1;  // 进入ratio输入模式
    input_index = 0;
}

/*!
    \brief      阈值设置命令
    \param[in]  none
    \param[out] none
    \retval     none
*/
void limit_set_command(void)
{
    extern uint8_t input_mode;
    extern uint8_t input_index;

    printf("limit=%.2f\r\n", g_config_params.limit);
    printf("Input value(0~500):");
    input_mode = 2;  // 进入limit输入模式
    input_index = 0;
}

/*!
    \brief      保存配置到Flash命令
    \param[in]  none
    \param[out] none
    \retval     none
*/
void config_save_command(void)
{
    printf("ratio: %.1f\r\n", g_config_params.ratio);
    printf("limit: %.2f\r\n", g_config_params.limit);
    printf("save parameters to flash\r\n");
    save_config_to_flash();
}

/*!
    \brief      从Flash读取配置命令
    \param[in]  none
    \param[out] none
    \retval     none
*/
void config_read_flash_command(void)
{
    printf("read parameters from flash\r\n");
    load_config_from_flash();
    printf("ratio: %.1f\r\n", g_config_params.ratio);
    printf("limit: %.2f\r\n", g_config_params.limit);
}

/*!
    \brief      解析配置文件
    \param[in]  none
    \param[out] none
    \retval     1: 成功, 0: 失败
*/
uint8_t parse_config_file(void)
{
    FIL file;
    FRESULT res;
    char line[100];
    UINT bytes_read;

    // 打开config.ini文件
    res = f_open(&file, "config.ini", FA_READ);
    if(res != FR_OK) {
        return 0;  // 文件不存在
    }

    // 逐行读取文件
    while(f_gets(line, sizeof(line), &file)) {
        // 解析Ratio
        if(strstr(line, "Ch0 =") && strstr(line, "[Ratio]") == NULL) {
            char* value_str = strchr(line, '=');
            if(value_str) {
                value_str++;  // 跳过'='
                while(*value_str == ' ') value_str++;  // 跳过空格
                g_config_params.ratio = atof(value_str);
            }
        }
        // 解析Limit
        else if(strstr(line, "Ch0=") && strstr(line, "[Limit]") == NULL) {
            char* value_str = strchr(line, '=');
            if(value_str) {
                value_str++;  // 跳过'='
                while(*value_str == ' ') value_str++;  // 跳过空格
                g_config_params.limit = atof(value_str);
            }
        }
    }

    f_close(&file);
    return 1;  // 成功
}

/*!
    \brief      保存配置到Flash
    \param[in]  none
    \param[out] none
    \retval     none
*/
void save_config_to_flash(void)
{
    uint8_t buffer[16];
    uint32_t magic = CONFIG_MAGIC_NUMBER;

    // 先擦除扇区
    spi_flash_sector_erase(CONFIG_FLASH_ADDR);

    // 写入魔数
    memcpy(buffer, &magic, 4);
    // 写入配置参数
    memcpy(buffer + 4, &g_config_params.ratio, 4);
    memcpy(buffer + 8, &g_config_params.limit, 4);

    // 写入Flash
    spi_flash_buffer_write(buffer, CONFIG_FLASH_ADDR, 12);
}

/*!
    \brief      从Flash加载配置
    \param[in]  none
    \param[out] none
    \retval     none
*/
void load_config_from_flash(void)
{
    uint8_t buffer[16];
    uint32_t magic;

    // 从Flash读取数据
    spi_flash_buffer_read(buffer, CONFIG_FLASH_ADDR, 12);

    // 检查魔数
    memcpy(&magic, buffer, 4);
    if(magic == CONFIG_MAGIC_NUMBER) {
        // 读取配置参数
        memcpy(&g_config_params.ratio, buffer + 4, 4);
        memcpy(&g_config_params.limit, buffer + 8, 4);
    } else {
        // 使用默认值
        g_config_params.ratio = 1.0f;
        g_config_params.limit = 1.0f;
    }
}

/*!
    \brief      验证变比值
    \param[in]  value: 要验证的值
    \param[out] none
    \retval     1: 有效, 0: 无效
*/
uint8_t validate_ratio(float value)
{
    return (value >= 0.0f && value <= 100.0f) ? 1 : 0;
}

/*!
    \brief      验证阈值
    \param[in]  value: 要验证的值
    \param[out] none
    \retval     1: 有效, 0: 无效
*/
uint8_t validate_limit(float value)
{
    return (value >= 0.0f && value <= 500.0f) ? 1 : 0;
}

/*!
    \brief      启动采样命令
    \param[in]  none
    \param[out] none
    \retval     none
*/
void sampling_start_command(void)
{
    g_sampling_active = 1;
    g_sampling_counter = 0;
    g_led_counter = 0;
    printf("Periodic Sampling\r\n");
    printf("sample cycle: %ds\r\n", g_sampling_config.sampling_cycle);

    // 记录日志
    store_log_data("Sampling started");
}

/*!
    \brief      停止采样命令
    \param[in]  none
    \param[out] none
    \retval     none
*/
void sampling_stop_command(void)
{
    g_sampling_active = 0;
    g_led1_state = 0;
    LED1_OFF();
    LED2_OFF();  // 同时关闭超限指示灯
    printf("Periodic Sampling STOP\r\n");

    // 记录日志
    store_log_data("Sampling stopped");
}

/*!
    \brief      按键扫描处理
    \param[in]  none
    \param[out] none
    \retval     none
*/
void key_scan_process(void)
{
    static uint32_t key_delay = 0;

    if(key_delay > 0) {
        key_delay--;
        return;
    }

    // KEY1: 采样启停控制
    if(KEY_Stat(KEY_PORT, KEY1_PIN)) {
        if(g_sampling_active) {
            sampling_stop_command();
        } else {
            sampling_start_command();
        }
        key_delay = 50;  // 防抖延时
    }

    // KEY2: 设置5秒周期
    if(KEY_Stat(KEY_PORT, KEY2_PIN)) {
        g_sampling_config.sampling_cycle = 5;
        save_sampling_config();
        printf("sample cycle adjust: 5s\r\n");
        store_log_data("Sample cycle set to 5s");
        key_delay = 50;
    }

    // KEY3: 设置10秒周期
    if(KEY_Stat(KEY_PORT, KEY3_PIN)) {
        g_sampling_config.sampling_cycle = 10;
        save_sampling_config();
        printf("sample cycle adjust: 10s\r\n");
        store_log_data("Sample cycle set to 10s");
        key_delay = 50;
    }

    // KEY4: 设置15秒周期
    if(KEY_Stat(KEY_PORT, KEY4_PIN)) {
        g_sampling_config.sampling_cycle = 15;
        save_sampling_config();
        printf("sample cycle adjust: 15s\r\n");
        store_log_data("Sample cycle set to 15s");
        key_delay = 50;
    }
}

/*!
    \brief      采样处理
    \param[in]  none
    \param[out] none
    \retval     none
*/
void sampling_process(void)
{
    if(g_sampling_active) {
        // LED1闪烁控制（1秒周期）
        g_led_counter++;
        if(g_led_counter >= 100) {  // 100*10ms = 1000ms = 1秒
            g_led1_state = !g_led1_state;
            if(g_led1_state) {
                LED1_ON();
            } else {
                LED1_OFF();
            }
            g_led_counter = 0;
        }

        // 采样控制
        g_sampling_counter++;
        if(g_sampling_counter >= g_sampling_config.sampling_cycle * 100) {  // 周期*100*10ms
            // 执行ADC采样
            adc_flag_clear(ADC0, ADC_FLAG_EOC);
            while(SET != adc_flag_get(ADC0, ADC_FLAG_EOC)) {}
            adc_value = ADC_RDATA(ADC0);
            Vol_Value = adc_value * 3.3f / 4095.0f * g_config_params.ratio;  // 应用变比

            // 获取当前时间
            rtc_current_time_get(&rtc_initpara);

            // 检查是否超限
            uint8_t over_limit = (Vol_Value > g_config_params.limit) ? 1 : 0;

            // 根据模式输出采样数据
            if(g_hide_mode) {
                // 隐藏模式：输出16进制数据
                uint32_t timestamp = rtc_to_unix_timestamp();
                print_hex_data(timestamp, Vol_Value, over_limit);
            } else {
                // 正常模式：输出可读格式
                printf("20%02d-%02d-%02d %02d:%02d:%02d ch0=%.2fV",
                       rtc_initpara.year, rtc_initpara.month, rtc_initpara.date,
                       rtc_initpara.hour, rtc_initpara.minute, rtc_initpara.second,
                       Vol_Value);

                // 检查超限并显示
                check_over_limit(Vol_Value);
                printf("\r\n");
            }

            // 存储采样数据
            store_sample_data(Vol_Value, over_limit);

            g_sampling_counter = 0;
        }
    } else {
        // 非采样状态，确保LED关闭
        LED1_OFF();
        LED2_OFF();
        g_sampling_counter = 0;
        g_led_counter = 0;
    }
}

/*!
    \brief      更新OLED显示
    \param[in]  none
    \param[out] none
    \retval     none
*/
void update_oled_display(void)
{
    static uint32_t display_counter = 0;
    char time_str[20];
    char voltage_str[20];

    display_counter++;
    // 每10次循环更新一次显示（10*10ms = 100ms）
    if(display_counter >= 10) {
        if(g_sampling_active) {
            // 采样状态：显示时间和电压
            rtc_current_time_get(&rtc_initpara);
            sprintf(time_str, "%02d:%02d:%02d",
                    rtc_initpara.hour, rtc_initpara.minute, rtc_initpara.second);

            // 实时获取电压值
            adc_flag_clear(ADC0, ADC_FLAG_EOC);
            while(SET != adc_flag_get(ADC0, ADC_FLAG_EOC)) {}
            adc_value = ADC_RDATA(ADC0);
            Vol_Value = adc_value * 3.3f / 4095.0f * g_config_params.ratio;

            sprintf(voltage_str, "%.2f V", Vol_Value);

            OLED_ShowString(0, 0, (uint8_t*)time_str, 16);
            OLED_ShowString(0, 16, (uint8_t*)voltage_str, 16);
        } else {
            // 非采样状态：显示system idle
            OLED_ShowString(0, 0, "system idle", 16);
            OLED_ShowString(0, 16, "", 16);
        }
        OLED_Refresh();
        display_counter = 0;
    }
}

/*!
    \brief      检查超限
    \param[in]  voltage: 电压值
    \param[out] none
    \retval     none
*/
void check_over_limit(float voltage)
{
    if(voltage > g_config_params.limit) {
        LED2_ON();
        printf(" OverLimit(%.2f)!", g_config_params.limit);
    } else {
        LED2_OFF();
    }
}

/*!
    \brief      保存采样配置
    \param[in]  none
    \param[out] none
    \retval     none
*/
void save_sampling_config(void)
{
    uint8_t buffer[8];
    uint32_t magic = SAMPLING_MAGIC_NUMBER;

    // 先擦除扇区
    spi_flash_sector_erase(SAMPLING_CONFIG_ADDR);

    // 写入魔数和配置
    memcpy(buffer, &magic, 4);
    memcpy(buffer + 4, &g_sampling_config.sampling_cycle, 1);

    // 写入Flash
    spi_flash_buffer_write(buffer, SAMPLING_CONFIG_ADDR, 8);
}

/*!
    \brief      加载采样配置
    \param[in]  none
    \param[out] none
    \retval     none
*/
void load_sampling_config(void)
{
    uint8_t buffer[8];
    uint32_t magic;

    // 从Flash读取数据
    spi_flash_buffer_read(buffer, SAMPLING_CONFIG_ADDR, 8);

    // 检查魔数
    memcpy(&magic, buffer, 4);
    if(magic == SAMPLING_MAGIC_NUMBER) {
        // 读取配置
        memcpy(&g_sampling_config.sampling_cycle, buffer + 4, 1);
        // 验证配置有效性
        if(g_sampling_config.sampling_cycle != 5 &&
           g_sampling_config.sampling_cycle != 10 &&
           g_sampling_config.sampling_cycle != 15) {
            g_sampling_config.sampling_cycle = 5;  // 默认值
        }
    } else {
        // 使用默认值
        g_sampling_config.sampling_cycle = 5;
    }
}

/*!
    \brief      数据隐藏命令
    \param[in]  none
    \param[out] none
    \retval     none
*/
void hide_data_command(void)
{
    g_hide_mode = 1;
    printf("Data hide mode enabled\r\n");

    // 记录日志
    store_log_data("Hide mode enabled");
}

/*!
    \brief      数据恢复命令
    \param[in]  none
    \param[out] none
    \retval     none
*/
void unhide_data_command(void)
{
    g_hide_mode = 0;
    printf("Data hide mode disabled\r\n");

    // 记录日志
    store_log_data("Hide mode disabled");
}

/*!
    \brief      RTC转Unix时间戳
    \param[in]  none
    \param[out] none
    \retval     Unix时间戳
*/
uint32_t rtc_to_unix_timestamp(void)
{
    // 获取当前RTC时间
    rtc_current_time_get(&rtc_initpara);

    // 将BCD格式转换为十进制
    uint32_t year = 2000 + ((rtc_initpara.year >> 4) * 10) + (rtc_initpara.year & 0x0F);
    uint32_t month = ((rtc_initpara.month >> 4) * 10) + (rtc_initpara.month & 0x0F);
    uint32_t day = ((rtc_initpara.date >> 4) * 10) + (rtc_initpara.date & 0x0F);
    uint32_t hour = ((rtc_initpara.hour >> 4) * 10) + (rtc_initpara.hour & 0x0F);
    uint32_t minute = ((rtc_initpara.minute >> 4) * 10) + (rtc_initpara.minute & 0x0F);
    uint32_t second = ((rtc_initpara.second >> 4) * 10) + (rtc_initpara.second & 0x0F);

    // 简化的Unix时间戳计算（从2000年开始）
    // 这是一个简化版本，实际应用中可能需要更精确的计算
    uint32_t days_since_2000 = 0;

    // 计算年份贡献的天数
    for(uint32_t y = 2000; y < year; y++) {
        if((y % 4 == 0 && y % 100 != 0) || (y % 400 == 0)) {
            days_since_2000 += 366; // 闰年
        } else {
            days_since_2000 += 365; // 平年
        }
    }

    // 每月天数（平年）
    uint32_t days_in_month[] = {31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};

    // 检查当前年是否为闰年
    if((year % 4 == 0 && year % 100 != 0) || (year % 400 == 0)) {
        days_in_month[1] = 29; // 闰年2月有29天
    }

    // 计算月份贡献的天数
    for(uint32_t m = 1; m < month; m++) {
        days_since_2000 += days_in_month[m - 1];
    }

    // 加上当前月的天数
    days_since_2000 += (day - 1);

    // 转换为秒数（从2000-01-01 00:00:00开始）
    uint32_t seconds_since_2000 = days_since_2000 * 86400 + hour * 3600 + minute * 60 + second;

    // Unix时间戳从1970-01-01开始，需要加上2000年之前的秒数
    // 从1970-01-01到2000-01-01的秒数：946684800
    return seconds_since_2000 + 946684800;
}

/*!
    \brief      电压转换为16进制格式
    \param[in]  voltage: 电压值
    \param[out] hex_data: 输出的16进制数据（4字节）
    \retval     none
*/
void voltage_to_hex(float voltage, uint8_t* hex_data)
{
    // 分离整数部分和小数部分
    uint16_t integer_part = (uint16_t)voltage;
    float decimal_part = voltage - integer_part;

    // 整数部分：2字节（高位在前）
    hex_data[0] = (integer_part >> 8) & 0xFF;  // 高字节
    hex_data[1] = integer_part & 0xFF;         // 低字节

    // 小数部分：2字节（高位在前）
    // 小数部分 * 65536 转换为16位整数
    uint16_t decimal_scaled = (uint16_t)(decimal_part * 65536);
    hex_data[2] = (decimal_scaled >> 8) & 0xFF;  // 高字节
    hex_data[3] = decimal_scaled & 0xFF;         // 低字节
}

/*!
    \brief      打印16进制数据
    \param[in]  timestamp: Unix时间戳
    \param[in]  voltage: 电压值
    \param[in]  over_limit: 是否超限
    \param[out] none
    \retval     none
*/
void print_hex_data(uint32_t timestamp, float voltage, uint8_t over_limit)
{
    uint8_t voltage_hex[4];

    // 转换电压为16进制格式
    voltage_to_hex(voltage, voltage_hex);

    // 打印时间戳（4字节，高位在前）
    printf("%02X%02X%02X%02X",
           (timestamp >> 24) & 0xFF,
           (timestamp >> 16) & 0xFF,
           (timestamp >> 8) & 0xFF,
           timestamp & 0xFF);

    // 打印电压值（4字节）
    printf("%02X%02X%02X%02X",
           voltage_hex[0], voltage_hex[1],
           voltage_hex[2], voltage_hex[3]);

    // 如果超限，添加*标记
    if(over_limit) {
        printf("*");
    }

    printf("\r\n");
}

/*!
    \brief      初始化数据存储系统
    \param[in]  none
    \param[out] none
    \retval     none
*/
void data_storage_init(void)
{
    // 创建必要的文件夹
    create_directories();

    // 获取下一个日志ID并创建日志文件
    uint32_t log_id = get_next_log_id();
    sprintf(g_data_storage.log_file, "log/log%d.txt", log_id);

    // 保存新的日志ID
    save_log_id(log_id + 1);

    // 记录系统启动日志
    store_log_data("System startup");

    // 初始化文件计数器
    g_data_storage.sample_file_count = 0;
    g_data_storage.overlimit_file_count = 0;
    g_data_storage.hidedata_file_count = 0;

    // 清空当前文件名
    memset(g_data_storage.current_sample_file, 0, sizeof(g_data_storage.current_sample_file));
    memset(g_data_storage.current_overlimit_file, 0, sizeof(g_data_storage.current_overlimit_file));
    memset(g_data_storage.current_hidedata_file, 0, sizeof(g_data_storage.current_hidedata_file));
}

/*!
    \brief      创建必要的文件夹
    \param[in]  none
    \param[out] none
    \retval     none
*/
void create_directories(void)
{
    // 创建sample文件夹
    f_mkdir("sample");

    // 创建overLimit文件夹
    f_mkdir("overLimit");

    // 创建log文件夹
    f_mkdir("log");

    // 创建hideData文件夹
    f_mkdir("hideData");
}

/*!
    \brief      存储采样数据
    \param[in]  voltage: 电压值
    \param[in]  over_limit: 是否超限
    \param[out] none
    \retval     none
*/
void store_sample_data(float voltage, uint8_t over_limit)
{
    // 如果是隐藏模式，存储到hideData文件夹
    if(g_hide_mode) {
        write_sample_file("hideData", "hideData", voltage, over_limit);
    } else {
        // 正常模式，存储到sample文件夹
        write_sample_file("sample", "sampleData", voltage, over_limit);
    }

    // 如果超限，同时存储到overLimit文件夹
    if(over_limit) {
        write_sample_file("overLimit", "overLimit", voltage, over_limit);
    }
}

/*!
    \brief      写入采样文件
    \param[in]  folder: 文件夹名
    \param[in]  prefix: 文件名前缀
    \param[in]  voltage: 电压值
    \param[in]  over_limit: 是否超限
    \param[out] none
    \retval     none
*/
void write_sample_file(const char* folder, const char* prefix, float voltage, uint8_t over_limit)
{
    FIL file;
    FRESULT res;
    char filepath[100];
    char data_line[200];
    char datetime_str[20];
    uint32_t* file_count;
    char* current_file;
    UINT bytes_written;

    // 根据文件夹选择对应的计数器和文件名变量
    if(strcmp(folder, "sample") == 0) {
        file_count = &g_data_storage.sample_file_count;
        current_file = g_data_storage.current_sample_file;
    } else if(strcmp(folder, "overLimit") == 0) {
        file_count = &g_data_storage.overlimit_file_count;
        current_file = g_data_storage.current_overlimit_file;
    } else if(strcmp(folder, "hideData") == 0) {
        file_count = &g_data_storage.hidedata_file_count;
        current_file = g_data_storage.current_hidedata_file;
    } else {
        return; // 无效的文件夹
    }

    // 如果需要创建新文件（计数为0或达到10条）
    if(*file_count == 0 || *file_count >= 10) {
        get_datetime_string(datetime_str);
        sprintf(current_file, "%s%s.txt", prefix, datetime_str);
        *file_count = 0;
    }

    // 构建完整文件路径
    sprintf(filepath, "%s/%s", folder, current_file);

    // 准备数据行
    rtc_current_time_get(&rtc_initpara);

    if(strcmp(folder, "hideData") == 0) {
        // hideData文件夹：同时存储原始数据和加密数据
        uint32_t timestamp = rtc_to_unix_timestamp();
        uint8_t voltage_hex[4];
        voltage_to_hex(voltage, voltage_hex);

        sprintf(data_line, "20%02d-%02d-%02d %02d:%02d:%02d ch0=%.2fV%s | %08X%02X%02X%02X%02X%s\r\n",
                rtc_initpara.year, rtc_initpara.month, rtc_initpara.date,
                rtc_initpara.hour, rtc_initpara.minute, rtc_initpara.second,
                voltage, over_limit ? " OverLimit" : "",
                timestamp, voltage_hex[0], voltage_hex[1], voltage_hex[2], voltage_hex[3],
                over_limit ? "*" : "");
    } else {
        // 其他文件夹：存储原始格式
        sprintf(data_line, "20%02d-%02d-%02d %02d:%02d:%02d ch0=%.2fV%s\r\n",
                rtc_initpara.year, rtc_initpara.month, rtc_initpara.date,
                rtc_initpara.hour, rtc_initpara.minute, rtc_initpara.second,
                voltage, over_limit ? " OverLimit" : "");
    }

    // 打开文件（追加模式）
    res = f_open(&file, filepath, FA_WRITE | FA_OPEN_ALWAYS);
    if(res == FR_OK) {
        // 移动到文件末尾
        f_lseek(&file, f_size(&file));

        // 写入数据
        f_write(&file, data_line, strlen(data_line), &bytes_written);

        // 关闭文件
        f_close(&file);

        // 增加计数
        (*file_count)++;
    }
}

/*!
    \brief      存储日志数据
    \param[in]  log_msg: 日志消息
    \param[out] none
    \retval     none
*/
void store_log_data(const char* log_msg)
{
    FIL file;
    FRESULT res;
    char log_line[200];
    UINT bytes_written;

    // 准备日志行
    rtc_current_time_get(&rtc_initpara);
    sprintf(log_line, "20%02d-%02d-%02d %02d:%02d:%02d - %s\r\n",
            rtc_initpara.year, rtc_initpara.month, rtc_initpara.date,
            rtc_initpara.hour, rtc_initpara.minute, rtc_initpara.second,
            log_msg);

    // 打开日志文件（追加模式）
    res = f_open(&file, g_data_storage.log_file, FA_WRITE | FA_OPEN_ALWAYS);
    if(res == FR_OK) {
        // 移动到文件末尾
        f_lseek(&file, f_size(&file));

        // 写入日志
        f_write(&file, log_line, strlen(log_line), &bytes_written);

        // 关闭文件
        f_close(&file);
    }
}

/*!
    \brief      获取时间字符串
    \param[out] datetime_str: 输出的时间字符串
    \param[out] none
    \retval     none
*/
void get_datetime_string(char* datetime_str)
{
    rtc_current_time_get(&rtc_initpara);
    sprintf(datetime_str, "20%02d%02d%02d%02d%02d%02d",
            rtc_initpara.year, rtc_initpara.month, rtc_initpara.date,
            rtc_initpara.hour, rtc_initpara.minute, rtc_initpara.second);
}

/*!
    \brief      获取下一个日志ID
    \param[in]  none
    \param[out] none
    \retval     下一个日志ID
*/
uint32_t get_next_log_id(void)
{
    uint8_t buffer[8];
    uint32_t magic;
    uint32_t log_id = 0;

    // 从Flash读取数据
    spi_flash_buffer_read(buffer, LOG_ID_FLASH_ADDR, 8);

    // 检查魔数
    memcpy(&magic, buffer, 4);
    if(magic == LOG_ID_MAGIC_NUMBER) {
        // 读取日志ID
        memcpy(&log_id, buffer + 4, 4);
    }

    return log_id;
}

/*!
    \brief      保存日志ID
    \param[in]  log_id: 要保存的日志ID
    \param[out] none
    \retval     none
*/
void save_log_id(uint32_t log_id)
{
    uint8_t buffer[8];
    uint32_t magic = LOG_ID_MAGIC_NUMBER;

    // 先擦除扇区
    spi_flash_sector_erase(LOG_ID_FLASH_ADDR);

    // 写入魔数和日志ID
    memcpy(buffer, &magic, 4);
    memcpy(buffer + 4, &log_id, 4);

    // 写入Flash
    spi_flash_buffer_write(buffer, LOG_ID_FLASH_ADDR, 8);
}




