/************************************************************
 * 锟斤拷权锟斤拷2025CIMC Copyright锟斤拷 
 * 锟侥硷拷锟斤拷Function.h
 * 锟斤拷锟斤拷: Lingyu Meng
 * 平台: 2025CIMC IHD-V04
 * 锟芥本: Lingyu Meng     2025/2/16     V0.01    original
************************************************************/

#ifndef __FUNCTION_H
#define __FUNCTION_H

/************************* 头文件 *************************/

#include <stdint.h>
#include <stdio.h>
#include "ff.h"  // FatFS文件系统类型定义

/************************* 锟疥定锟斤拷 *************************/


/************************ 锟斤拷锟斤拷锟斤拷锟斤拷 ************************/


/************************ 函数声明 ************************/

void System_Init(void);      	// 系统初始化
void UsrFunction(void);         // 用户函数
void system_selftest(void);     // 系统自检
void rtc_config_command(char* time_str);  // RTC配置命令
void rtc_now_command(void);     // RTC当前时间命令
void process_uart_command(char* cmd);  // 处理串口命令
uint8_t parse_time_string(char* time_str, uint32_t* year, uint32_t* month, uint32_t* day, uint32_t* hour, uint32_t* minute, uint32_t* second);  // 解析时间字符串

// 配置管理相关函数
void config_read_command(void);    // 读取配置文件
void ratio_set_command(void);      // 设置变比命令
void limit_set_command(void);      // 设置阈值命令
void config_save_command(void);    // 保存配置到Flash
void config_read_flash_command(void); // 从Flash读取配置
uint8_t parse_config_file(void);   // 解析配置文件
void save_config_to_flash(void);   // 保存配置到Flash
void load_config_from_flash(void); // 从Flash加载配置
uint8_t validate_ratio(float value); // 验证变比值
uint8_t validate_limit(float value); // 验证阈值

// 采样控制相关函数
void sampling_start_command(void);     // 启动采样
void sampling_stop_command(void);      // 停止采样
void key_scan_process(void);           // 按键扫描处理
void sampling_process(void);           // 采样处理
void update_oled_display(void);        // 更新OLED显示
void check_over_limit(float voltage);  // 检查超限
void save_sampling_config(void);       // 保存采样配置
void load_sampling_config(void);       // 加载采样配置

// 数据处理相关函数
void hide_data_command(void);           // 数据隐藏指令
void unhide_data_command(void);         // 数据恢复指令
uint32_t rtc_to_unix_timestamp(void);   // RTC到Unix时间戳转换
void voltage_to_hex(float voltage, uint8_t* hex_data); // 电压转换为16进制
void print_hex_data(uint32_t timestamp, float voltage, uint8_t over_limit); // 打印16进制数据

// 数据存储相关函数
void data_storage_init(void);           // 初始化数据存储系统
void store_sample_data(float voltage, uint8_t over_limit); // 存储采样数据
void store_log_data(const char* log_msg); // 存储日志数据
void create_directories(void);          // 创建必要的文件夹
void get_datetime_string(char* datetime_str); // 获取时间字符串
uint32_t get_next_log_id(void);         // 获取下一个日志ID
void save_log_id(uint32_t log_id);      // 保存日志ID
void write_sample_file(const char* folder, const char* prefix, float voltage, uint8_t over_limit); // 写入数据文件
#define BUFFER_SIZE              256
#define TX_BUFFER_SIZE           (countof(tx_buffer) - 1)
#define RX_BUFFER_SIZE           0xFF
#define CMD_BUFFER_SIZE          50   // 命令缓冲区大小

// 配置参数结构体
typedef struct {
    float ratio;    // 变比值
    float limit;    // 阈值
} config_params_t;

// Flash存储地址定义
#define CONFIG_FLASH_ADDR        0x001000  // 配置参数存储地址
#define SAMPLING_CONFIG_ADDR     0x002000  // 采样配置存储地址
#define CONFIG_MAGIC_NUMBER      0x12345678 // 魔数用于验证数据有效性
#define SAMPLING_MAGIC_NUMBER    0x87654321 // 采样配置魔数
#define LOG_ID_FLASH_ADDR        0x003000  // 日志ID存储地址
#define LOG_ID_MAGIC_NUMBER      0xABCDEF12 // 日志ID魔数

// 全局变量
extern config_params_t g_config_params;
extern FATFS g_fatfs;  // FatFS文件系统对象
extern uint8_t input_mode;
extern char input_buffer[20];
extern uint8_t input_index;

// 采样配置结构体
typedef struct {
    uint8_t sampling_cycle;  // 采样周期：5/10/15秒
} sampling_config_t;

// 全局变量
extern uint8_t g_sampling_active;      // 采样状态标志
extern uint8_t g_led1_state;           // LED1状态
extern uint32_t g_sampling_counter;    // 采样计数器
extern uint32_t g_led_counter;         // LED闪烁计数器
extern uint32_t g_system_tick;         // 系统滴答计数器
extern sampling_config_t g_sampling_config; // 采样配置
extern uint8_t g_hide_mode;             // 数据隐藏模式标志

// 数据存储结构体
typedef struct {
    uint32_t sample_file_count;     // 当前采样文件中的数据条数
    uint32_t overlimit_file_count;  // 当前超限文件中的数据条数
    uint32_t hidedata_file_count;   // 当前隐藏文件中的数据条数
    char current_sample_file[50];   // 当前采样文件名
    char current_overlimit_file[50]; // 当前超限文件名
    char current_hidedata_file[50];  // 当前隐藏文件名
    char log_file[30];              // 日志文件名
} data_storage_t;

extern data_storage_t g_data_storage;

#define countof(a)               (sizeof(a) / sizeof(*(a)))

#define SFLASH_ID                0xC84013
#define FLASH_WRITE_ADDRESS      0x000000
#define FLASH_READ_ADDRESS       FLASH_WRITE_ADDRESS


void turn_on_led(uint8_t led_num);
void get_chip_serial_num(void);
ErrStatus memory_compare(uint8_t *src, uint8_t *dst, uint16_t length);
void test_status_led_init(void);

#endif


/****************************End******************************/

