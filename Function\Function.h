/************************************************************
 * 锟斤拷权锟斤拷2025CIMC Copyright锟斤拷 
 * 锟侥硷拷锟斤拷Function.h
 * 锟斤拷锟斤拷: Lingyu Meng
 * 平台: 2025CIMC IHD-V04
 * 锟芥本: Lingyu Meng     2025/2/16     V0.01    original
************************************************************/

#ifndef __FUNCTION_H
#define __FUNCTION_H

/************************* 头锟侥硷拷 *************************/

#include "HeaderFiles.h"

/************************* 锟疥定锟斤拷 *************************/


/************************ 锟斤拷锟斤拷锟斤拷锟斤拷 ************************/


/************************ 锟斤拷锟斤拷锟斤拷锟斤拷 ************************/

void System_Init(void);      	// 系统锟斤拷始锟斤拷
void UsrFunction(void);         // 锟矫伙拷锟斤拷锟斤拷
void system_selftest(void);     // 系统锟皆硷拷
void rtc_config_command(char* time_str);  // RTC锟斤拷锟斤拷锟�
void rtc_now_command(void);     // RTC锟斤拷前时锟斤拷锟斤拷锟�
void process_uart_command(char* cmd);  // 锟斤拷锟斤拷锟斤拷锟斤拷诳锟斤拷锟�
uint8_t parse_time_string(char* time_str, uint32_t* year, uint32_t* month, uint32_t* day, uint32_t* hour, uint32_t* minute, uint32_t* second);  // 锟斤拷锟斤拷时锟斤拷锟街凤拷锟斤拷

// ���ù���������
void config_read_command(void);    // ��ȡ�����ļ�
void ratio_set_command(void);      // ���ñȲ���
void limit_set_command(void);      // ������ֵ����
void config_save_command(void);    // ���������Flash
void config_read_flash_command(void); // ��Flash��ȡ����
uint8_t parse_config_file(void);   // ������������
void save_config_to_flash(void);   // ���������Flash
void load_config_from_flash(void); // ��Flash��������
uint8_t validate_ratio(float value); // ��֤���ȱ�
uint8_t validate_limit(float value); // ��֤��ֵ
#define BUFFER_SIZE              256
#define TX_BUFFER_SIZE           (countof(tx_buffer) - 1)
#define RX_BUFFER_SIZE           0xFF
#define CMD_BUFFER_SIZE          50   // 锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷

// ���ò���ṹ��
typedef struct {
    float ratio;    // ���ȱ�
    float limit;    // ��ֵ
} config_params_t;

// Flash�洢��ַ����
#define CONFIG_FLASH_ADDR        0x001000  // ���ò���洢��ַ
#define CONFIG_MAGIC_NUMBER      0x12345678 // ħ������֤����Ч��

// ȫ�ֱ���
extern config_params_t g_config_params;
extern FATFS g_fatfs;  // FatFS�ļ�ϵͳ����
extern uint8_t input_mode;
extern char input_buffer[20];
extern uint8_t input_index;

#define countof(a)               (sizeof(a) / sizeof(*(a)))

#define SFLASH_ID                0xC84013
#define FLASH_WRITE_ADDRESS      0x000000
#define FLASH_READ_ADDRESS       FLASH_WRITE_ADDRESS


void turn_on_led(uint8_t led_num);
void get_chip_serial_num(void);
ErrStatus memory_compare(uint8_t *src, uint8_t *dst, uint16_t length);
void test_status_led_init(void);

#endif


/****************************锟斤拷锟斤拷******************************/

