数据存储格式示例

1. sample文件夹示例 (sampleData20250101003010.txt):
2025-01-01 00:30:10 ch0=10.50V
2025-01-01 00:30:15 ch0=10.52V
2025-01-01 00:30:20 ch0=10.48V
2025-01-01 00:30:25 ch0=10.51V
2025-01-01 00:30:30 ch0=10.49V
2025-01-01 00:30:35 ch0=10.53V
2025-01-01 00:30:40 ch0=10.47V
2025-01-01 00:30:45 ch0=10.50V
2025-01-01 00:30:50 ch0=10.52V
2025-01-01 00:30:55 ch0=10.48V

2. overLimit文件夹示例 (overLimit20250101003100.txt):
2025-01-01 00:31:00 ch0=15.20V OverLimit
2025-01-01 00:31:05 ch0=15.35V OverLimit
2025-01-01 00:31:10 ch0=15.18V OverLimit
2025-01-01 00:31:15 ch0=15.42V OverLimit
2025-01-01 00:31:20 ch0=15.28V OverLimit

3. log文件夹示例 (log0.txt):
2025-01-01 00:30:00 - System startup
2025-01-01 00:30:05 - Sampling started
2025-01-01 00:31:00 - Hide mode enabled
2025-01-01 00:32:00 - Hide mode disabled
2025-01-01 00:33:00 - Sampling stopped

4. hideData文件夹示例 (hideData20250101003200.txt):
2025-01-01 00:32:00 ch0=12.50V | 6774C4F0000C8000
2025-01-01 00:32:05 ch0=12.52V | 6774C4F5000C851F
2025-01-01 00:32:10 ch0=12.48V | 6774C4FA000C7AE1
2025-01-01 00:32:15 ch0=15.20V OverLimit | 6774C4FF000F3333*
2025-01-01 00:32:20 ch0=12.51V | 6774C504000C8147

文件夹结构:
TF卡根目录/
├── sample/
│   ├── sampleData20250101003010.txt
│   ├── sampleData20250101003020.txt
│   └── ...
├── overLimit/
│   ├── overLimit20250101003100.txt
│   ├── overLimit20250101003200.txt
│   └── ...
├── log/
│   ├── log0.txt
│   ├── log1.txt
│   └── ...
└── hideData/
    ├── hideData20250101003200.txt
    ├── hideData20250101003300.txt
    └── ...

存储规则说明:
1. 每个数据文件最多存储10条记录
2. 超过10条后自动创建新文件
3. 文件名中的datetime为文件创建时间
4. 日志文件在每次上电时创建新文件
5. 日志ID持久化存储在Flash中
6. 隐藏模式时sample文件夹不存储数据
7. 超限数据始终存储在overLimit文件夹中
