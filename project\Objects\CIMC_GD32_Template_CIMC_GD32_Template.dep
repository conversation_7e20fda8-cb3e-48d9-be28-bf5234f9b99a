Dependencies for Project 'CIMC_GD32_Template', Target 'CIMC_GD32_Template': (DO NOT MODIFY !)
CompilerVersion: 5060750::V5.06 update 6 (build 750)::ARMCC
F (..\User\gd32f4xx_it.c)(0x684EC6DB)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-ID:\keilarm\ARM\PACK\ARM\CMSIS\6.1.0\CMSIS\Core\Include

-ID:\keilarm\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="542" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_it.o --omf_browse .\objects\gd32f4xx_it.crf --depend .\objects\gd32f4xx_it.d)
I (..\User\gd32f4xx_it.h)(0x61ADC346)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
I (..\User\systick.h)(0x61ADC344)
I (..\Function\Function.h)(0x684ECF52)
I (..\HeaderFiles\HeaderFiles.h)(0x684ED051)
I (D:\keilarm\ARM\ARMCC\include\stdio.h)(0x599ECD2C)
I (D:\keilarm\ARM\ARMCC\include\string.h)(0x599ECD2C)
I (..\HardWare\KEY.h)(0x67DF7744)
I (..\HardWare\LED\LED.h)(0x684D6E9E)
I (..\HardWare\USART0.h)(0x684D6B46)
I (..\HardWare\tim.h)(0x684D65E0)
I (..\HardWare\mode.h)(0x684D7BEA)
I (..\HardWare\ADC.h)(0x684D44B6)
I (..\HardWare\oled.h)(0x684D44B6)
I (..\HardWare\SPI_FLASH.h)(0x6811B370)
I (..\HardWare\RTC.h)(0x684EC243)
I (..\HardWare\sdcard.h)(0x6811903A)
I (..\Fatfs\ff.h)(0x4E646136)
I (..\Fatfs\integer.h)(0x4BD31212)
I (..\Fatfs\ffconf.h)(0x4E64F36A)
F (..\User\main.c)(0x67DE7C82)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-ID:\keilarm\ARM\PACK\ARM\CMSIS\6.1.0\CMSIS\Core\Include

-ID:\keilarm\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="542" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\main.o --omf_browse .\objects\main.crf --depend .\objects\main.d)
I (..\HeaderFiles\HeaderFiles.h)(0x684ED051)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
I (..\User\systick.h)(0x61ADC344)
I (D:\keilarm\ARM\ARMCC\include\stdio.h)(0x599ECD2C)
I (D:\keilarm\ARM\ARMCC\include\string.h)(0x599ECD2C)
I (..\Function\Function.h)(0x684ECF52)
I (..\HardWare\KEY.h)(0x67DF7744)
I (..\HardWare\LED\LED.h)(0x684D6E9E)
I (..\HardWare\USART0.h)(0x684D6B46)
I (..\HardWare\tim.h)(0x684D65E0)
I (..\HardWare\mode.h)(0x684D7BEA)
I (..\HardWare\ADC.h)(0x684D44B6)
I (..\HardWare\oled.h)(0x684D44B6)
I (..\HardWare\SPI_FLASH.h)(0x6811B370)
I (..\HardWare\RTC.h)(0x684EC243)
I (..\HardWare\sdcard.h)(0x6811903A)
I (..\Fatfs\ff.h)(0x4E646136)
I (..\Fatfs\integer.h)(0x4BD31212)
I (..\Fatfs\ffconf.h)(0x4E64F36A)
F (..\User\systick.c)(0x61ADC344)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-ID:\keilarm\ARM\PACK\ARM\CMSIS\6.1.0\CMSIS\Core\Include

-ID:\keilarm\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="542" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\systick.o --omf_browse .\objects\systick.crf --depend .\objects\systick.d)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
I (..\User\systick.h)(0x61ADC344)
F (..\HardWare\LED\LED.c)(0x684D6E8A)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-ID:\keilarm\ARM\PACK\ARM\CMSIS\6.1.0\CMSIS\Core\Include

-ID:\keilarm\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="542" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\led.o --omf_browse .\objects\led.crf --depend .\objects\led.d)
I (..\HardWare\LED\led.h)(0x684D6E9E)
I (..\HeaderFiles\HeaderFiles.h)(0x684ED051)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
I (..\User\systick.h)(0x61ADC344)
I (D:\keilarm\ARM\ARMCC\include\stdio.h)(0x599ECD2C)
I (D:\keilarm\ARM\ARMCC\include\string.h)(0x599ECD2C)
I (..\Function\Function.h)(0x684ECF52)
I (..\HardWare\KEY.h)(0x67DF7744)
I (..\HardWare\USART0.h)(0x684D6B46)
I (..\HardWare\tim.h)(0x684D65E0)
I (..\HardWare\mode.h)(0x684D7BEA)
I (..\HardWare\ADC.h)(0x684D44B6)
I (..\HardWare\oled.h)(0x684D44B6)
I (..\HardWare\SPI_FLASH.h)(0x6811B370)
I (..\HardWare\RTC.h)(0x684EC243)
I (..\HardWare\sdcard.h)(0x6811903A)
I (..\Fatfs\ff.h)(0x4E646136)
I (..\Fatfs\integer.h)(0x4BD31212)
I (..\Fatfs\ffconf.h)(0x4E64F36A)
F (..\HardWare\LED\LED.h)(0x684D6E9E)()
F (..\HardWare\key.c)(0x684D6098)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-ID:\keilarm\ARM\PACK\ARM\CMSIS\6.1.0\CMSIS\Core\Include

-ID:\keilarm\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="542" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\key.o --omf_browse .\objects\key.crf --depend .\objects\key.d)
I (..\HardWare\key.h)(0x67DF7744)
I (..\HeaderFiles\HeaderFiles.h)(0x684ED051)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
I (..\User\systick.h)(0x61ADC344)
I (D:\keilarm\ARM\ARMCC\include\stdio.h)(0x599ECD2C)
I (D:\keilarm\ARM\ARMCC\include\string.h)(0x599ECD2C)
I (..\Function\Function.h)(0x684ECF52)
I (..\HardWare\LED\LED.h)(0x684D6E9E)
I (..\HardWare\USART0.h)(0x684D6B46)
I (..\HardWare\tim.h)(0x684D65E0)
I (..\HardWare\mode.h)(0x684D7BEA)
I (..\HardWare\ADC.h)(0x684D44B6)
I (..\HardWare\oled.h)(0x684D44B6)
I (..\HardWare\SPI_FLASH.h)(0x6811B370)
I (..\HardWare\RTC.h)(0x684EC243)
I (..\HardWare\sdcard.h)(0x6811903A)
I (..\Fatfs\ff.h)(0x4E646136)
I (..\Fatfs\integer.h)(0x4BD31212)
I (..\Fatfs\ffconf.h)(0x4E64F36A)
F (..\HardWare\key.h)(0x67DF7744)()
F (..\HardWare\USART0.c)(0x684D6BCA)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-ID:\keilarm\ARM\PACK\ARM\CMSIS\6.1.0\CMSIS\Core\Include

-ID:\keilarm\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="542" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\usart0.o --omf_browse .\objects\usart0.crf --depend .\objects\usart0.d)
I (..\HardWare\USART0.h)(0x684D6B46)
I (..\HeaderFiles\HeaderFiles.h)(0x684ED051)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
I (..\User\systick.h)(0x61ADC344)
I (D:\keilarm\ARM\ARMCC\include\stdio.h)(0x599ECD2C)
I (D:\keilarm\ARM\ARMCC\include\string.h)(0x599ECD2C)
I (..\Function\Function.h)(0x684ECF52)
I (..\HardWare\KEY.h)(0x67DF7744)
I (..\HardWare\LED\LED.h)(0x684D6E9E)
I (..\HardWare\tim.h)(0x684D65E0)
I (..\HardWare\mode.h)(0x684D7BEA)
I (..\HardWare\ADC.h)(0x684D44B6)
I (..\HardWare\oled.h)(0x684D44B6)
I (..\HardWare\SPI_FLASH.h)(0x6811B370)
I (..\HardWare\RTC.h)(0x684EC243)
I (..\HardWare\sdcard.h)(0x6811903A)
I (..\Fatfs\ff.h)(0x4E646136)
I (..\Fatfs\integer.h)(0x4BD31212)
I (..\Fatfs\ffconf.h)(0x4E64F36A)
F (..\HardWare\USART0.h)(0x684D6B46)()
F (..\HardWare\mode.c)(0x684ED0B7)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-ID:\keilarm\ARM\PACK\ARM\CMSIS\6.1.0\CMSIS\Core\Include

-ID:\keilarm\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="542" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\mode.o --omf_browse .\objects\mode.crf --depend .\objects\mode.d)
I (..\HardWare\mode.h)(0x684D7BEA)
I (..\HeaderFiles\HeaderFiles.h)(0x684ED051)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
I (..\User\systick.h)(0x61ADC344)
I (D:\keilarm\ARM\ARMCC\include\stdio.h)(0x599ECD2C)
I (D:\keilarm\ARM\ARMCC\include\string.h)(0x599ECD2C)
I (..\Function\Function.h)(0x684ECF52)
I (..\HardWare\KEY.h)(0x67DF7744)
I (..\HardWare\LED\LED.h)(0x684D6E9E)
I (..\HardWare\USART0.h)(0x684D6B46)
I (..\HardWare\tim.h)(0x684D65E0)
I (..\HardWare\ADC.h)(0x684D44B6)
I (..\HardWare\oled.h)(0x684D44B6)
I (..\HardWare\SPI_FLASH.h)(0x6811B370)
I (..\HardWare\RTC.h)(0x684EC243)
I (..\HardWare\sdcard.h)(0x6811903A)
I (..\Fatfs\ff.h)(0x4E646136)
I (..\Fatfs\integer.h)(0x4BD31212)
I (..\Fatfs\ffconf.h)(0x4E64F36A)
F (..\HardWare\mode.h)(0x684D7BEA)()
F (..\HardWare\tim.c)(0x684D6840)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-ID:\keilarm\ARM\PACK\ARM\CMSIS\6.1.0\CMSIS\Core\Include

-ID:\keilarm\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="542" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\tim.o --omf_browse .\objects\tim.crf --depend .\objects\tim.d)
I (..\HardWare\tim.h)(0x684D65E0)
I (..\HeaderFiles\HeaderFiles.h)(0x684ED051)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
I (..\User\systick.h)(0x61ADC344)
I (D:\keilarm\ARM\ARMCC\include\stdio.h)(0x599ECD2C)
I (D:\keilarm\ARM\ARMCC\include\string.h)(0x599ECD2C)
I (..\Function\Function.h)(0x684ECF52)
I (..\HardWare\KEY.h)(0x67DF7744)
I (..\HardWare\LED\LED.h)(0x684D6E9E)
I (..\HardWare\USART0.h)(0x684D6B46)
I (..\HardWare\mode.h)(0x684D7BEA)
I (..\HardWare\ADC.h)(0x684D44B6)
I (..\HardWare\oled.h)(0x684D44B6)
I (..\HardWare\SPI_FLASH.h)(0x6811B370)
I (..\HardWare\RTC.h)(0x684EC243)
I (..\HardWare\sdcard.h)(0x6811903A)
I (..\Fatfs\ff.h)(0x4E646136)
I (..\Fatfs\integer.h)(0x4BD31212)
I (..\Fatfs\ffconf.h)(0x4E64F36A)
F (..\HardWare\tim.h)(0x684D65E0)()
F (..\HardWare\ADC.c)(0x684D744E)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-ID:\keilarm\ARM\PACK\ARM\CMSIS\6.1.0\CMSIS\Core\Include

-ID:\keilarm\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="542" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\adc.o --omf_browse .\objects\adc.crf --depend .\objects\adc.d)
I (..\HardWare\ADC.h)(0x684D44B6)
I (..\HeaderFiles\HeaderFiles.h)(0x684ED051)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
I (..\User\systick.h)(0x61ADC344)
I (D:\keilarm\ARM\ARMCC\include\stdio.h)(0x599ECD2C)
I (D:\keilarm\ARM\ARMCC\include\string.h)(0x599ECD2C)
I (..\Function\Function.h)(0x684ECF52)
I (..\HardWare\KEY.h)(0x67DF7744)
I (..\HardWare\LED\LED.h)(0x684D6E9E)
I (..\HardWare\USART0.h)(0x684D6B46)
I (..\HardWare\tim.h)(0x684D65E0)
I (..\HardWare\mode.h)(0x684D7BEA)
I (..\HardWare\oled.h)(0x684D44B6)
I (..\HardWare\SPI_FLASH.h)(0x6811B370)
I (..\HardWare\RTC.h)(0x684EC243)
I (..\HardWare\sdcard.h)(0x6811903A)
I (..\Fatfs\ff.h)(0x4E646136)
I (..\Fatfs\integer.h)(0x4BD31212)
I (..\Fatfs\ffconf.h)(0x4E64F36A)
F (..\HardWare\ADC.h)(0x684D44B6)()
F (..\HardWare\OLED.c)(0x684D44B6)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-ID:\keilarm\ARM\PACK\ARM\CMSIS\6.1.0\CMSIS\Core\Include

-ID:\keilarm\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="542" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\oled.o --omf_browse .\objects\oled.crf --depend .\objects\oled.d)
I (..\HardWare\oled.h)(0x684D44B6)
I (..\HeaderFiles\HeaderFiles.h)(0x684ED051)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
I (..\User\systick.h)(0x61ADC344)
I (D:\keilarm\ARM\ARMCC\include\stdio.h)(0x599ECD2C)
I (D:\keilarm\ARM\ARMCC\include\string.h)(0x599ECD2C)
I (..\Function\Function.h)(0x684ECF52)
I (..\HardWare\KEY.h)(0x67DF7744)
I (..\HardWare\LED\LED.h)(0x684D6E9E)
I (..\HardWare\USART0.h)(0x684D6B46)
I (..\HardWare\tim.h)(0x684D65E0)
I (..\HardWare\mode.h)(0x684D7BEA)
I (..\HardWare\ADC.h)(0x684D44B6)
I (..\HardWare\SPI_FLASH.h)(0x6811B370)
I (..\HardWare\RTC.h)(0x684EC243)
I (..\HardWare\sdcard.h)(0x6811903A)
I (..\Fatfs\ff.h)(0x4E646136)
I (..\Fatfs\integer.h)(0x4BD31212)
I (..\Fatfs\ffconf.h)(0x4E64F36A)
I (..\HardWare\oledfont.h)(0x684D44B6)
F (..\HardWare\OLED.h)(0x684D44B6)()
F (..\HardWare\OLEDfont.h)(0x684D44B6)()
F (..\HardWare\RTC.c)(0x684D7B5A)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-ID:\keilarm\ARM\PACK\ARM\CMSIS\6.1.0\CMSIS\Core\Include

-ID:\keilarm\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="542" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\rtc.o --omf_browse .\objects\rtc.crf --depend .\objects\rtc.d)
I (..\HardWare\RTC.h)(0x684EC243)
I (..\HeaderFiles\HeaderFiles.h)(0x684ED051)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
I (..\User\systick.h)(0x61ADC344)
I (D:\keilarm\ARM\ARMCC\include\stdio.h)(0x599ECD2C)
I (D:\keilarm\ARM\ARMCC\include\string.h)(0x599ECD2C)
I (..\Function\Function.h)(0x684ECF52)
I (..\HardWare\KEY.h)(0x67DF7744)
I (..\HardWare\LED\LED.h)(0x684D6E9E)
I (..\HardWare\USART0.h)(0x684D6B46)
I (..\HardWare\tim.h)(0x684D65E0)
I (..\HardWare\mode.h)(0x684D7BEA)
I (..\HardWare\ADC.h)(0x684D44B6)
I (..\HardWare\oled.h)(0x684D44B6)
I (..\HardWare\SPI_FLASH.h)(0x6811B370)
I (..\HardWare\sdcard.h)(0x6811903A)
I (..\Fatfs\ff.h)(0x4E646136)
I (..\Fatfs\integer.h)(0x4BD31212)
I (..\Fatfs\ffconf.h)(0x4E64F36A)
F (..\HardWare\RTC.h)(0x684EC243)()
F (..\HardWare\sdcard.c)(0x5E86E15E)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-ID:\keilarm\ARM\PACK\ARM\CMSIS\6.1.0\CMSIS\Core\Include

-ID:\keilarm\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="542" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\sdcard.o --omf_browse .\objects\sdcard.crf --depend .\objects\sdcard.d)
I (..\HardWare\sdcard.h)(0x6811903A)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stddef.h)(0x599ECD2C)
F (..\HardWare\sdcard.h)(0x6811903A)()
F (..\Function\Function.c)(0x684ED032)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-ID:\keilarm\ARM\PACK\ARM\CMSIS\6.1.0\CMSIS\Core\Include

-ID:\keilarm\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="542" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\function.o --omf_browse .\objects\function.crf --depend .\objects\function.d)
I (..\Function\Function.h)(0x684ECF52)
I (..\HeaderFiles\HeaderFiles.h)(0x684ED051)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
I (..\User\systick.h)(0x61ADC344)
I (D:\keilarm\ARM\ARMCC\include\stdio.h)(0x599ECD2C)
I (D:\keilarm\ARM\ARMCC\include\string.h)(0x599ECD2C)
I (..\HardWare\KEY.h)(0x67DF7744)
I (..\HardWare\LED\LED.h)(0x684D6E9E)
I (..\HardWare\USART0.h)(0x684D6B46)
I (..\HardWare\tim.h)(0x684D65E0)
I (..\HardWare\mode.h)(0x684D7BEA)
I (..\HardWare\ADC.h)(0x684D44B6)
I (..\HardWare\oled.h)(0x684D44B6)
I (..\HardWare\SPI_FLASH.h)(0x6811B370)
I (..\HardWare\RTC.h)(0x684EC243)
I (..\HardWare\sdcard.h)(0x6811903A)
I (..\Fatfs\ff.h)(0x4E646136)
I (..\Fatfs\integer.h)(0x4BD31212)
I (..\Fatfs\ffconf.h)(0x4E64F36A)
F (..\HeaderFiles\HeaderFiles.h)(0x684ED051)()
F (..\CMSIS\GD\GD32F4xx\Source\system_gd32f4xx.c)(0x67D91E04)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-ID:\keilarm\ARM\PACK\ARM\CMSIS\6.1.0\CMSIS\Core\Include

-ID:\keilarm\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="542" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\system_gd32f4xx.o --omf_browse .\objects\system_gd32f4xx.crf --depend .\objects\system_gd32f4xx.d)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_adc.c)(0x65A7AAB8)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-ID:\keilarm\ARM\PACK\ARM\CMSIS\6.1.0\CMSIS\Core\Include

-ID:\keilarm\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="542" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_adc.o --omf_browse .\objects\gd32f4xx_adc.crf --depend .\objects\gd32f4xx_adc.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_can.c)(0x67D917C2)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-ID:\keilarm\ARM\PACK\ARM\CMSIS\6.1.0\CMSIS\Core\Include

-ID:\keilarm\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="542" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_can.o --omf_browse .\objects\gd32f4xx_can.crf --depend .\objects\gd32f4xx_can.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_crc.c)(0x65A7AAB8)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-ID:\keilarm\ARM\PACK\ARM\CMSIS\6.1.0\CMSIS\Core\Include

-ID:\keilarm\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="542" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_crc.o --omf_browse .\objects\gd32f4xx_crc.crf --depend .\objects\gd32f4xx_crc.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_ctc.c)(0x65A7AAB8)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-ID:\keilarm\ARM\PACK\ARM\CMSIS\6.1.0\CMSIS\Core\Include

-ID:\keilarm\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="542" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_ctc.o --omf_browse .\objects\gd32f4xx_ctc.crf --depend .\objects\gd32f4xx_ctc.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_dac.c)(0x65A7AAB8)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-ID:\keilarm\ARM\PACK\ARM\CMSIS\6.1.0\CMSIS\Core\Include

-ID:\keilarm\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="542" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_dac.o --omf_browse .\objects\gd32f4xx_dac.crf --depend .\objects\gd32f4xx_dac.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_dbg.c)(0x65A7AAB8)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-ID:\keilarm\ARM\PACK\ARM\CMSIS\6.1.0\CMSIS\Core\Include

-ID:\keilarm\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="542" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_dbg.o --omf_browse .\objects\gd32f4xx_dbg.crf --depend .\objects\gd32f4xx_dbg.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_dci.c)(0x65A7AAB8)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-ID:\keilarm\ARM\PACK\ARM\CMSIS\6.1.0\CMSIS\Core\Include

-ID:\keilarm\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="542" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_dci.o --omf_browse .\objects\gd32f4xx_dci.crf --depend .\objects\gd32f4xx_dci.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_dma.c)(0x65A7AAB8)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-ID:\keilarm\ARM\PACK\ARM\CMSIS\6.1.0\CMSIS\Core\Include

-ID:\keilarm\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="542" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_dma.o --omf_browse .\objects\gd32f4xx_dma.crf --depend .\objects\gd32f4xx_dma.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_enet.c)(0x65A7AAB8)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-ID:\keilarm\ARM\PACK\ARM\CMSIS\6.1.0\CMSIS\Core\Include

-ID:\keilarm\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="542" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_enet.o --omf_browse .\objects\gd32f4xx_enet.crf --depend .\objects\gd32f4xx_enet.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_exmc.c)(0x65A7AAB8)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-ID:\keilarm\ARM\PACK\ARM\CMSIS\6.1.0\CMSIS\Core\Include

-ID:\keilarm\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="542" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_exmc.o --omf_browse .\objects\gd32f4xx_exmc.crf --depend .\objects\gd32f4xx_exmc.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_exti.c)(0x65A7AAB8)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-ID:\keilarm\ARM\PACK\ARM\CMSIS\6.1.0\CMSIS\Core\Include

-ID:\keilarm\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="542" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_exti.o --omf_browse .\objects\gd32f4xx_exti.crf --depend .\objects\gd32f4xx_exti.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_fmc.c)(0x65A7AAB8)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-ID:\keilarm\ARM\PACK\ARM\CMSIS\6.1.0\CMSIS\Core\Include

-ID:\keilarm\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="542" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_fmc.o --omf_browse .\objects\gd32f4xx_fmc.crf --depend .\objects\gd32f4xx_fmc.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_fwdgt.c)(0x65A7AAB8)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-ID:\keilarm\ARM\PACK\ARM\CMSIS\6.1.0\CMSIS\Core\Include

-ID:\keilarm\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="542" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_fwdgt.o --omf_browse .\objects\gd32f4xx_fwdgt.crf --depend .\objects\gd32f4xx_fwdgt.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_gpio.c)(0x65A7AAB8)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-ID:\keilarm\ARM\PACK\ARM\CMSIS\6.1.0\CMSIS\Core\Include

-ID:\keilarm\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="542" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_gpio.o --omf_browse .\objects\gd32f4xx_gpio.crf --depend .\objects\gd32f4xx_gpio.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_i2c.c)(0x65A7AAB8)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-ID:\keilarm\ARM\PACK\ARM\CMSIS\6.1.0\CMSIS\Core\Include

-ID:\keilarm\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="542" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_i2c.o --omf_browse .\objects\gd32f4xx_i2c.crf --depend .\objects\gd32f4xx_i2c.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_ipa.c)(0x65A7AAB8)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-ID:\keilarm\ARM\PACK\ARM\CMSIS\6.1.0\CMSIS\Core\Include

-ID:\keilarm\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="542" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_ipa.o --omf_browse .\objects\gd32f4xx_ipa.crf --depend .\objects\gd32f4xx_ipa.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_iref.c)(0x65A7AAB8)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-ID:\keilarm\ARM\PACK\ARM\CMSIS\6.1.0\CMSIS\Core\Include

-ID:\keilarm\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="542" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_iref.o --omf_browse .\objects\gd32f4xx_iref.crf --depend .\objects\gd32f4xx_iref.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_misc.c)(0x65A7AAB8)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-ID:\keilarm\ARM\PACK\ARM\CMSIS\6.1.0\CMSIS\Core\Include

-ID:\keilarm\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="542" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_misc.o --omf_browse .\objects\gd32f4xx_misc.crf --depend .\objects\gd32f4xx_misc.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_pmu.c)(0x65A7AAB8)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-ID:\keilarm\ARM\PACK\ARM\CMSIS\6.1.0\CMSIS\Core\Include

-ID:\keilarm\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="542" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_pmu.o --omf_browse .\objects\gd32f4xx_pmu.crf --depend .\objects\gd32f4xx_pmu.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_rcu.c)(0x65A7AAB8)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-ID:\keilarm\ARM\PACK\ARM\CMSIS\6.1.0\CMSIS\Core\Include

-ID:\keilarm\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="542" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_rcu.o --omf_browse .\objects\gd32f4xx_rcu.crf --depend .\objects\gd32f4xx_rcu.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_rtc.c)(0x65A7AAB8)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-ID:\keilarm\ARM\PACK\ARM\CMSIS\6.1.0\CMSIS\Core\Include

-ID:\keilarm\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="542" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_rtc.o --omf_browse .\objects\gd32f4xx_rtc.crf --depend .\objects\gd32f4xx_rtc.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_sdio.c)(0x65A7AAB8)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-ID:\keilarm\ARM\PACK\ARM\CMSIS\6.1.0\CMSIS\Core\Include

-ID:\keilarm\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="542" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_sdio.o --omf_browse .\objects\gd32f4xx_sdio.crf --depend .\objects\gd32f4xx_sdio.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_spi.c)(0x65A7AAB8)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-ID:\keilarm\ARM\PACK\ARM\CMSIS\6.1.0\CMSIS\Core\Include

-ID:\keilarm\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="542" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_spi.o --omf_browse .\objects\gd32f4xx_spi.crf --depend .\objects\gd32f4xx_spi.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_syscfg.c)(0x65A7AAB8)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-ID:\keilarm\ARM\PACK\ARM\CMSIS\6.1.0\CMSIS\Core\Include

-ID:\keilarm\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="542" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_syscfg.o --omf_browse .\objects\gd32f4xx_syscfg.crf --depend .\objects\gd32f4xx_syscfg.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_timer.c)(0x65A7AAB8)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-ID:\keilarm\ARM\PACK\ARM\CMSIS\6.1.0\CMSIS\Core\Include

-ID:\keilarm\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="542" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_timer.o --omf_browse .\objects\gd32f4xx_timer.crf --depend .\objects\gd32f4xx_timer.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_tli.c)(0x65A7AAB8)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-ID:\keilarm\ARM\PACK\ARM\CMSIS\6.1.0\CMSIS\Core\Include

-ID:\keilarm\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="542" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_tli.o --omf_browse .\objects\gd32f4xx_tli.crf --depend .\objects\gd32f4xx_tli.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_trng.c)(0x65A7AAB8)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-ID:\keilarm\ARM\PACK\ARM\CMSIS\6.1.0\CMSIS\Core\Include

-ID:\keilarm\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="542" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_trng.o --omf_browse .\objects\gd32f4xx_trng.crf --depend .\objects\gd32f4xx_trng.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_usart.c)(0x65A7AAB8)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-ID:\keilarm\ARM\PACK\ARM\CMSIS\6.1.0\CMSIS\Core\Include

-ID:\keilarm\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="542" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_usart.o --omf_browse .\objects\gd32f4xx_usart.crf --depend .\objects\gd32f4xx_usart.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_wwdgt.c)(0x65A7AAB8)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-ID:\keilarm\ARM\PACK\ARM\CMSIS\6.1.0\CMSIS\Core\Include

-ID:\keilarm\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="542" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_wwdgt.o --omf_browse .\objects\gd32f4xx_wwdgt.crf --depend .\objects\gd32f4xx_wwdgt.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
F (..\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.s)(0x65A7AAB6)(--cpu Cortex-M4.fp.sp -g --apcs=interwork --pd "__MICROLIB SETA 1"

-I.\RTE\_CIMC_GD32_Template

-ID:\keilarm\ARM\PACK\ARM\CMSIS\6.1.0\CMSIS\Core\Include

-ID:\keilarm\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

--pd "__UVISION_VERSION SETA 542" --pd "GD32F470 SETA 1" --pd "_RTE_ SETA 1"

--list .\listings\startup_gd32f450_470.lst --xref -o .\objects\startup_gd32f450_470.o --depend .\objects\startup_gd32f450_470.d)
F (..\01 Readme\Readme.txt)(0x684ECF00)()
F (..\Fatfs\diskio.c)(0x5C8751B4)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-ID:\keilarm\ARM\PACK\ARM\CMSIS\6.1.0\CMSIS\Core\Include

-ID:\keilarm\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="542" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\diskio.o --omf_browse .\objects\diskio.crf --depend .\objects\diskio.d)
I (..\Fatfs\diskio.h)(0x4D21CB12)
I (..\Fatfs\integer.h)(0x4BD31212)
I (..\HardWare\sdcard.h)(0x6811903A)
I (..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x67D920AE)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdint.h)(0x599ECD2E)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x67D9195A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x67D91244)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdlib.h)(0x599ECD2C)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x65A7AAB6)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x65A7AAB6)
I (D:\keilarm\ARM\ARMCC\include\stdio.h)(0x599ECD2C)
F (..\Fatfs\ff.c)(0x4E64E464)(--c99 --gnu -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Function -I ..\HardWare\LED -I ..\CMSIS\GD\GD32F4xx\Include -I ..\HardWare -I ..\Fatfs

-I.\RTE\_CIMC_GD32_Template

-ID:\keilarm\ARM\PACK\ARM\CMSIS\6.1.0\CMSIS\Core\Include

-ID:\keilarm\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="542" -DGD32F470 -D_RTE_ -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\ff.o --omf_browse .\objects\ff.crf --depend .\objects\ff.d)
I (..\Fatfs\ff.h)(0x4E646136)
I (..\Fatfs\integer.h)(0x4BD31212)
I (..\Fatfs\ffconf.h)(0x4E64F36A)
I (..\Fatfs\diskio.h)(0x4D21CB12)
F (..\Fatfs\00readme.txt)(0x4E64F382)()
F (..\Fatfs\diskio.h)(0x4D21CB12)()
F (..\Fatfs\ff.h)(0x4E646136)()
F (..\Fatfs\ffconf.h)(0x4E64F36A)()
F (..\Fatfs\integer.h)(0x4BD31212)()
