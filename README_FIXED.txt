# 嵌入式系统综合功能实现

## 硬件平台

基于GD32F470VET6微控制器的嵌入式开发板

硬件配置：
 - GD32F470VET6 MCU
 - SPI Flash存储器
 - SD卡接口
 - RTC实时时钟
 - OLED显示屏
 - LED指示灯
 - 按键输入
 - 串口通信
 
## 功能简介

基础模板，包含基础的外设驱动程序。
新增系统自检和RTC时间设置功能。
新增配置管理系统。
新增数据处理系统。
新增采样控制系统。
新增数据存储系统。

## 实现功能

基础实现。

### 新增功能：

#### 1. 系统自检功能
通过串口输入"test"指令，系统将进行自检并输出结果:
- Flash检测：检测SPI Flash是否正常工作，显示Flash ID
- TF卡检测：检测SD卡是否存在，显示存储容量
- RTC时间显示：显示当前RTC时间

输出格式示例：
```
======system selftest======
fflash............ok 
TFcard.......ok 
flash ID:0xC84013
TF card memory:8192KB
RTC:2025-01-01 01:00:50
======system selftest======
```

#### 2. RTC时间设置功能
- **设置时间：**通过串口输入"RTC Config 2025-01-01 12:00:30"格式的指令设置RTC时间
  - 支持多种时间格式：
    - "2025-01-01 12:00:30"
    - "2025 01 01 12 00 30"  
    - "2025-01-01 12-00-30"
  - 成功设置后返回："RTC Config success\nTime:2025-01-01 12:00:30"

- **查看当前时间：**通过串口输入"RTC now"查看当前时间
  - 返回格式："Current Time: 2025-01-01 12:00:30"

#### 3. 配置管理系统
- **读取配置文件：**通过串口输入"conf"指令，从TF卡读取config.ini文件
  - 成功读取显示："Ratio = 10.5\nLimit = 100\nconfig read success"
  - 文件不存在返回："config.ini file not found."

- **变比设置：**通过串口输入"ratio"指令设置变比值（0-100范围）
  - 显示当前值并提示输入新值
  - 有效输入显示"ratio modified success"
  - 无效输入显示"ratio invalid"

- **阈值设置：**通过串口输入"limit"指令设置变比值（0-500范围）
  - 显示当前值并提示输入新值
  - 有效输入显示"limit modified success"
  - 无效输入显示"limit invalid"

- **配置存储：**通过串口输入"config save"指令，保存配置到Flash
  - 显示当前配置值并提示"save parameters to flash"

- **配置读取：**通过串口输入"config read"指令，从Flash读取配置
  - 显示"read parameters from flash"及当前配置值

#### 4. 采样控制系统
- **采样启动（串口）：**通过串口输入"start"指令启动周期采样
  - LED1闪烁指示（1秒周期）
  - OLED显示时间（hh:mm:ss格式）和电压值（xx.xx V格式）
  - 串口输出显示"Periodic Sampling\nsample cycle: 5s"
  - 每个采样周期输出采样数据"2025-01-01 00:30:05 ch0=10.5V"

- **停止采样（串口）：**通过串口输入"stop"指令停止采样
  - LED1常灭
  - OLED显示"system idle"
  - 串口输出显示"Periodic Sampling STOP"

- **采样控制（按键）：**通过按KEY1按键控制采样状态翻转
  - 当前停止状态下，按KEY1开始采样
  - 当前采样状态下，按KEY1停止采样

- **周期调整：**通过按KEY2/KEY3/KEY4按键设置采样周期（5s/10s/15s）
  - KEY2设置5秒周期
  - KEY3设置10秒周期  
  - KEY4设置15秒周期
  - 配置持久化，断电重启后有效
  - 串口输出显示"sample cycle adjust: 10s"

- **超限提示：**当电压值超过设置的limit阈值时：
  - 点亮LED2
  - 串口输出显示"OverLimit(10.00)!"信息
  - 显示格式："2025-01-01 00:30:05 ch0=10.5V OverLimit(10.00)!"

#### 5. 数据处理系统
- **数据隐藏：**通过串口输入"hide"指令启用数据隐藏模式
  - 时间戳转换为Unix时间戳（4字节16进制）
  - 电压值转换为特定格式（4字节16进制）
    - 整数部分：2字节，高位在前
    - 小数部分：2字节，高位在前，小数部分×65536
  - 输出显示："6774C4F5000C8000"
  - 超限时在末尾添加"*"标记："6774C4F5000C8000*"

- **数据恢复：**通过串口输入"unhide"指令恢复正常模式
  - 恢复到正常显示格式
  - 显示"Data hide mode disabled"

### 数据编码说明：
- **时间戳转换：**2025-01-01 12:30:45 → Unix时间戳 1735705845 → 16进制 6774C4F5
- **电压转换：**12.5V → 整数部分 12(000C) + 小数部分 0.5*65536=32768(8000) → 000C8000
- **完整示例：**6774C4F5000C8000 表示 2025-01-01 12:30:45 ch0=12.5V
- **超限显示：**超限时在末尾添加"*"号：6774C4F5000C8000*

#### 6. 数据存储系统
- **采样数据存储：**TF卡下sample文件夹中存储采样数据
  - 每个文件存储10条数据
  - 文件命名格式：sampleData{datetime}.txt
  - datetime格式：20250101003010（14位数字）

- **超阈值数据存储：**TF卡下overLimit文件夹中存储超限数据
  - 每个文件存储10条数据
  - 文件命名格式：overLimit{datetime}.txt
  - 只存储超过阈值的数据

- **日志存储：**TF卡下log文件夹中存储操作日志
  - 每次上电创建一个文件
  - 文件命名格式：log{id}.txt，id从0开始自增
  - ID号持久化存储在MCU的Flash中

- **加密数据存储：**TF卡下hideData文件夹中存储加密数据
  - 每个文件存储10条数据
  - 文件命名格式：hideData{datetime}.txt
  - 同时存储未加密数据和加密数据用于校验准确性
  - 隐藏模式时sample文件夹中不存储数据

### 文件存储格式：
- **sample文件：**2025-01-01 00:30:05 ch0=10.5V
- **overLimit文件：**2025-01-01 00:30:05 ch0=10.5V OverLimit
- **log文件：**2025-01-01 00:30:05 - Sampling started
- **hideData文件：**2025-01-01 00:30:05 ch0=10.5V | 6774C4F5000C8000

### 使用说明：
1. 通过串口工具连接设备（波特率115200）
2. 发送相应指令即可使用对应功能
3. 所有指令以回车符结束
4. 变比和阈值设置过程：
   - 输入"ratio"或"limit"指令
   - 显示当前值并提示输入新值
   - 输入有效范围内的值
   - 系统自动验证并给出响应

### 文件夹结构：
```
TF卡根目录/
├── sample/          # 正常采样数据
├── overLimit/       # 超限数据
├── log/            # 操作日志
└── hideData/       # 加密数据
```

### 技术特点：
- 完整的硬件抽象层
- 多模式数据处理和存储
- 实时采样和显示
- 配置参数持久化
- 智能文件管理
- 完整的日志记录
- 数据加密和校验
- 用户友好的交互界面
