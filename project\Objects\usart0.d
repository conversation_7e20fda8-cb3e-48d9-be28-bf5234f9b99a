.\objects\usart0.o: ..\HardWare\USART0.c
.\objects\usart0.o: ..\HardWare\USART0.h
.\objects\usart0.o: ..\HeaderFiles\HeaderFiles.h
.\objects\usart0.o: ..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\objects\usart0.o: ..\CMSIS\core_cm4.h
.\objects\usart0.o: D:\keilarm\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\usart0.o: ..\CMSIS\core_cmInstr.h
.\objects\usart0.o: ..\CMSIS\core_cmFunc.h
.\objects\usart0.o: ..\CMSIS\core_cm4_simd.h
.\objects\usart0.o: ..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h
.\objects\usart0.o: ..\User\gd32f4xx_libopt.h
.\objects\usart0.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h
.\objects\usart0.o: ..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\objects\usart0.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h
.\objects\usart0.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h
.\objects\usart0.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h
.\objects\usart0.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h
.\objects\usart0.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h
.\objects\usart0.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h
.\objects\usart0.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h
.\objects\usart0.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h
.\objects\usart0.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h
.\objects\usart0.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h
.\objects\usart0.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h
.\objects\usart0.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h
.\objects\usart0.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h
.\objects\usart0.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h
.\objects\usart0.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h
.\objects\usart0.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h
.\objects\usart0.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h
.\objects\usart0.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h
.\objects\usart0.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h
.\objects\usart0.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h
.\objects\usart0.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h
.\objects\usart0.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h
.\objects\usart0.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h
.\objects\usart0.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h
.\objects\usart0.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h
.\objects\usart0.o: D:\keilarm\ARM\ARMCC\Bin\..\include\stdlib.h
.\objects\usart0.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h
.\objects\usart0.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h
.\objects\usart0.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h
.\objects\usart0.o: ..\User\systick.h
.\objects\usart0.o: D:\keilarm\ARM\ARMCC\Bin\..\include\stdio.h
.\objects\usart0.o: D:\keilarm\ARM\ARMCC\Bin\..\include\string.h
.\objects\usart0.o: ..\Function\Function.h
.\objects\usart0.o: ..\HeaderFiles\HeaderFiles.h
.\objects\usart0.o: ..\Fatfs\ff.h
.\objects\usart0.o: ..\Fatfs\integer.h
.\objects\usart0.o: ..\Fatfs\ffconf.h
.\objects\usart0.o: ..\HardWare\KEY.h
.\objects\usart0.o: ..\HardWare\LED\LED.h
.\objects\usart0.o: ..\HardWare\USART0.h
.\objects\usart0.o: ..\HardWare\tim.h
.\objects\usart0.o: ..\HardWare\mode.h
.\objects\usart0.o: ..\HardWare\ADC.h
.\objects\usart0.o: ..\HardWare\oled.h
.\objects\usart0.o: ..\HardWare\SPI_FLASH.h
.\objects\usart0.o: ..\HardWare\RTC.h
.\objects\usart0.o: ..\HardWare\sdcard.h
