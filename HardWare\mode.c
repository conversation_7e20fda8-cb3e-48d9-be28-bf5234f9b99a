#include "mode.h"
#include <string.h>


// uint8_t flag_led, change_ledflag; // 已移至Function.c
uint8_t data_recv = 0;
char receive_buffer[10];
uint8_t buffer_index = 0;
uint8_t sampling_status = 0; // ����״̬��־λ��0 ��ʾֹͣ��1 ��ʾ����

void TIMER4_IRQHandler(void)
{
    if(SET == timer_interrupt_flag_get(TIMER4, TIMER_INT_FLAG_UP))
    {
        timer_interrupt_flag_clear(TIMER4, TIMER_INT_FLAG_UP);
        // 定时器中断处理，LED控制已移至Function.c中处理
    }
}

//void led_change(void)
//{
//    if(flag_led == 0){
//        LED1_OFF();
//    } else {
//        LED1_ON();
//    }
//}

/*
// USART0_IRQHandler ���ѵ� gd32f4xx_it.c �д���
void USART0_IRQHandler(void)
{
    if(RESET != usart_interrupt_flag_get(USART0, USART_INT_FLAG_RBNE))
    {
        data_recv = usart_data_receive(USART0);    //  ���մ�������
        usart_interrupt_flag_clear(USART0, USART_INT_FLAG_RBNE); 	// ��������жϱ�־λ

        // �����յ����ַ����ӵ�������
        if (buffer_index < 10 - 1) {
            receive_buffer[buffer_index++] = data_recv;
            receive_buffer[buffer_index] = '\0'; // ȷ���ַ����� '\0' ��β

            // ����Ƿ���յ������� "start" �� "stop" ���������з��Ƚ�����־
            if (strcmp(receive_buffer, "start") == 0) {
                sampling_status = 1; // ��������
                buffer_index = 0; // ��ջ�����
            } else if (strcmp(receive_buffer, "stop") == 0) {
                sampling_status = 0; // ֹͣ����
                LED1_OFF(); // ֹͣ����ʱ�ر� LED1
                buffer_index = 0; // ��ջ�����
            }else if (strcmp(receive_buffer, "RTC Config") == 0) {

            }else if (strcmp(receive_buffer, "RTC now") == 0) {

            }else if (data_recv == '\n' || data_recv == '\r') {
                buffer_index = 0; // �������з�����ջ�����
            }
        } else {
            buffer_index = 0; // ������������ջ�����
        }
    }
}
*/


void USART0_SendData(uint16_t *buf,uint16_t len)
{
    uint16_t t;
    for(t = 0; t < len; t++)      
    {           
        while(usart_flag_get(USART0, USART_FLAG_TC) == RESET);  
        usart_data_transmit(USART0,buf[t]);
    }     
    while(usart_flag_get(USART0, USART_FLAG_TC) == RESET);          
}
 
