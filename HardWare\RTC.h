/************************************************************
 * ��Ȩ��2025CIMC Copyright�� 
 * �ļ���RTC.h
 * ����: <PERSON><PERSON> @ GigaDevice
 * ƽ̨: 2025CIMC IHD-V04
 * �汾: Qiao Qin     2025/4/20     V0.01    original
************************************************************/

#ifndef __RTC_H
#define __RTC_H
#include "HeaderFiles.h"

void RTC_Init(void);	// RTC��ʼ��
void rtc_setup(void);	// RTCʱ������
void rtc_show_time(void);	// RTCʱ��
void rtc_show_alarm(void);	// RTC����
uint8_t usart_input_threshold(uint32_t value);  // ��������ֵ��ЧУ��
void rtc_pre_config(void);

// �ⲿ��������
extern rtc_parameter_struct rtc_initpara;
extern __IO uint32_t prescaler_a, prescaler_s;

#endif
