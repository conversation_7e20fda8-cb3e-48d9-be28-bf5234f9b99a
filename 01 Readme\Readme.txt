# 2025��CIMC�й�����������ս��-��ҵǶ��ʽϵͳ��������

# Program��CIMC_GD32_Template

## ������
- �������ƣ�GD32F470 DEMO ����ģ��
- ʵ��ƽ̨: CIMC IHD V0.4
- MDK�汾��5.25
CIMC_GD32_Template
## ������Դ

 - GD32F470VET6 MCU
 
 
## 功能简介

基础模板，包含基础的外设驱动程序。
新增系统自检和RTC时间设置功能。
新增配置管理系统。
新增数据处理系统。
新增采样控制系统。
新增数据存储系统。


## 实现功能

基础实现。

### 新增功能：

#### 1. 系统自检功能
通过串口输入"test"指令，系统将进行自检并输出结果:
- Flash检测：检测SPI Flash是否正常工作，显示Flash ID
- TF卡检测：检测SD卡是否存在，显示存储容量
- RTC时间显示：显示当前RTC时间

�����ʽʾ����
```
======system selftest======
fflash............ok
TFcard.......ok
flash ID:0xC84013
TF card memory:8192KB
RTC:2025-01-01 01:00:50
======system selftest======
```

#### 2. RTCʱ���������
- **����ʱ�䣺**ͨ�������������"RTC Config 2025-01-01 12:00:30"��ʽ��ָ������RTCʱ��
  - ֧�ֶ���ʱ���ʽ��
    - "2025-01-01 12:00:30"
    - "2025 01 01 12 00 30"
    - "2025-01-01 12-00-30"
  - �ɹ����ú󷵻أ�"RTC Config success\nTime:2025-01-01 12:00:30"

- **�鿴��ǰʱ�䣺**ͨ�������������"RTC now"�鿴��ǰʱ��
  - ���ظ�ʽ��"Current Time: 2025-01-01 12:00:30"

#### 3. ���ù���ϵͳ
- **��ȡ�����ļ���**ͨ �������������"conf"ָ�ӣ��ӿ�TF������ȡconfig.ini�ļ�
  - �ɹ���ȡ��ʾ��"Ratio = 10.5\nLimit = 100\nconfig read success"
  - �ļ������ڷ��أ�"config.ini file not found."

- **���ȱ����ã�**ͨ �������������"ratio"ָ������ñȱ�ֵ��0-100��Χ��
  - ��ʾ��ǰֵ������������ֵ
  - ��Ч����ʾ"ratio modified success"
  - ��Ч����ʾ"ratio invalid"

- **��ֵ���ã�**ͨ �������������"limit"ָ������ñȱ�ֵ��0-500��Χ��
  - ��ʾ��ǰֵ������������ֵ
  - ��Ч����ʾ"limit modified success"
  - ��Ч����ʾ"limit invalid"

- **���洢��**ͨ �������������"config save"ָ�ӣ��������Flash
  - ��ʾ��ǰ����ֵ������"save parameters to flash"

- **������ȡ��**ͨ �������������"config read"ָ�ӣ���Flash��ȡ����
  - ��ʾ"read parameters from flash"������ǰ����ֵ

### ʹ��˵����
1. ͨ�������߹ߵ������豸������115200��
2. ���������Ӧָ�����ʹ�ö�Ӧ����
3. ����ָ���Իس���������
4. ���ȱȺ���ֵ���ù��̣�
   - ����"ratio"��"limit"ָ��
   - ��ʾ��ǰֵ��������ֵ
   - ����Ч��Χ�ڵ�ֵ
   - ϵͳ��������֤��������Ӧ

#### 4. ��������ϵͳ
- **��������ڿ���**��ͨ �������������"start"ָ��������������
  - LED1��˸��ʾ��1������
  - OLED��ʾʱ�䣨hh:mm:ss��ʽ����ѹֵ��xx.xx V��ʽ��
  - ��������ڿ�����ʾ"Periodic Sampling\nsample cycle: 5s"
  - ÿ������ڿ�����ʾ��������"2025-01-01 00:30:05 ch0=10.5V"

- **ֹͣ��������ڿ���**��ͨ �������������"stop"ָ��ֹͣ����
  - LED1����
  - OLED��ʾ"system idle"
  - ��������ڿ�����ʾ"Periodic Sampling STOP"

- **��������ť����**��ͨ ��KEY1��ť���Ʋ���״̬��ת
  - ��ǰֹͣ״̬�£���KEY1��ʼ����
  - ��ǰ����״̬�£���KEY1ֹͣ����

- **��������**��ͨ ��KEY2/KEY3/KEY4��ť������������5s/10s/15s��
  - KEY2����5������
  - KEY3����10������
  - KEY4����15������
  - ���ó־û���ϵ�����Ч��
  - ��������ڿ�����ʾ"sample cycle adjust: 10s"

- **������ʾ**����ѹֵ������limit��ֵʱ��
  - ����LED2
  - ��������ڿ�����ʾ"OverLimit(10.00)!"��Ϣ
  - ��ʾ��ʽ��"2025-01-01 00:30:05 ch0=10.5V OverLimit(10.00)!"

#### 5. ���ݴ���ϵͳ
- **���ݶ���**��ͨ �������������"hide"ָ�����ݶ���ģʽ
  - ʱ���ת��Ϊ Unix ʱ���4�ֽ�16������
  - ��ѹֵת��Ϊ�ض���ʽ��4�ֽ�16������
    - ��������2�ֽڣ��߸�λ��ǰ��
    - С������2�ֽڣ��߸�λ��ǰ��С����*65536
  - ������ʾ��"6774C4F5000C8000"
  - ������ʱ��ĩβ����"*"��ǣ�"6774C4F5000C8000*"

- **���ݻָ�**��ͨ �������������"unhide"ָ��ָ�����ģʽ
  - �ָ���������ʾ��ʽ
  - ��ʾ"Data hide mode disabled"

### ���ݱ���˵����
- **ʱ���ת����**��2025-01-01 12:30:45 → Unix ʱ��� 1735705845 → 16������ 6774C4F5
- **��ѹת����**��12.5V → ������ 12(000C) + С���� 0.5*65536=32768(8000) → 000C8000
- **��������**��6774C4F5000C8000 ��ʾ 2025-01-01 12:30:45 ch0=12.5V
- **������ʾ**������ʱ��ĩβ����"*"��ţ�6774C4F5000C8000*

#### 6. ���ݴ洢ϵͳ
- **��������洢**��TF����sample�ļ����´洢��������
  - ÿ���ļ�洢10������
  - �ļ�����ʽ��sampleData{datetime}.txt
  - datetime��ʽ��20250101003010��14λ����

- **������ֵ���ݴ洢**��TF����overLimit�ļ����´洢������
  - ÿ���ļ�洢10������
  - �ļ�����ʽ��overLimit{datetime}.txt
  - ֻ洢������ֵ����

- **��־�洢**��TF����log�ļ����´洢��������־
  - ÿ�ι��µ�����һ���ļ�
  - �ļ�����ʽ��log{id}.txt��id��0��ʼ���Զ���
  - ID�ŵ־û���MCU��Flash��

- **�������ݴ洢**��TF����hideData�ļ����´洢��������
  - ÿ���ļ�洢10������
  - �ļ�����ʽ��hideData{datetime}.txt
  - ͬʱ洢δ�������ݺͼ�������У��׼ȷ��
  - ������ģʽ��sample�ļ����в�洢����

### �ļ��洢��ʽ��
- **sample�ļ���**��2025-01-01 00:30:05 ch0=10.5V
- **overLimit�ļ���**��2025-01-01 00:30:05 ch0=10.5V OverLimit
- **log�ļ���**��2025-01-01 00:30:05 - Sampling started
- **hideData�ļ���**��2025-01-01 00:30:05 ch0=10.5V | 6774C4F5000C8000

## ���ŷ���


## ����汾

- ����汾��V0.1
- �������ڣ�2025-03-22

## ��ϵ����

- Copyright   : CIMC�й�����������ս��
- Author      ��Lingyu Meng
- Website     ��www.siemenscup-cimc.org.cn
- Phone       ��15801122380

## ����

**�Ͻ���ҵ��;������ѧϰʹ�á� **


## Ŀ¼�ṹ

����01 Readme		������Ŀ˵��
����CMSIS			�ں������ļ���Cortex Microcontroller Software Interface Standard
����Function		�û�����
����HardWare		Ӳ������
����HeaderFiles	ͷ�ļ�����
����Library		���ļ�
��  ����GD32F4xx_standard_peripheral
��  ����GD32F4xx_usb_library
��  ����Third_Party
����project		�����ļ��������ɵ������ļ���
����Protocol		Э�����
����Startup		�����ļ�
����System		
����User
