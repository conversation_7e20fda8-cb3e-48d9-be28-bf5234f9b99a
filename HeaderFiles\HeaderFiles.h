/************************************************************
 * ��Ȩ��2025CIMC Copyright�� 
 * �ļ���Headerfiles.h
 * ����: Lingyu Meng
 * ƽ̨: 2025 CIMC IHD V04
 * �汾: Lingyu Meng     2023/2/16     V0.01    original
************************************************************/

#ifndef __HEADERFILES_H
#define __HEADERFILES_H

/************************* ͷ�ļ� *************************/

#include "gd32f4xx.h"
#include "gd32f4xx_libopt.h"
#include "systick.h"
#include <stdio.h>
#include <stdint.h>
#include <stdlib.h>
#include "string.h"
#include "Function.h"     // ִ�к���
#include "KEY.h"
#include "LED.h"
#include "USART0.h"
#include "tim.h"
#include "mode.h"
#include "ADC.h"
#include "oled.h"
#include "SPI_FLASH.h"  // SPI Flash驱动
#include "RTC.h"        // RTC驱动
#include "sdcard.h"     // SD卡驱动
#include "ff.h"         // FatFS文件系统


#endif

/****************************End*****************************/

