.\objects\mode.o: ..\HardWare\mode.c
.\objects\mode.o: ..\HardWare\mode.h
.\objects\mode.o: ..\HeaderFiles\HeaderFiles.h
.\objects\mode.o: ..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\objects\mode.o: ..\CMSIS\core_cm4.h
.\objects\mode.o: D:\keilarm\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\mode.o: ..\CMSIS\core_cmInstr.h
.\objects\mode.o: ..\CMSIS\core_cmFunc.h
.\objects\mode.o: ..\CMSIS\core_cm4_simd.h
.\objects\mode.o: ..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h
.\objects\mode.o: ..\User\gd32f4xx_libopt.h
.\objects\mode.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h
.\objects\mode.o: ..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\objects\mode.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h
.\objects\mode.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h
.\objects\mode.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h
.\objects\mode.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h
.\objects\mode.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h
.\objects\mode.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h
.\objects\mode.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h
.\objects\mode.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h
.\objects\mode.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h
.\objects\mode.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h
.\objects\mode.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h
.\objects\mode.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h
.\objects\mode.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h
.\objects\mode.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h
.\objects\mode.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h
.\objects\mode.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h
.\objects\mode.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h
.\objects\mode.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h
.\objects\mode.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h
.\objects\mode.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h
.\objects\mode.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h
.\objects\mode.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h
.\objects\mode.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h
.\objects\mode.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h
.\objects\mode.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h
.\objects\mode.o: D:\keilarm\ARM\ARMCC\Bin\..\include\stdlib.h
.\objects\mode.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h
.\objects\mode.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h
.\objects\mode.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h
.\objects\mode.o: ..\User\systick.h
.\objects\mode.o: D:\keilarm\ARM\ARMCC\Bin\..\include\stdio.h
.\objects\mode.o: D:\keilarm\ARM\ARMCC\Bin\..\include\string.h
.\objects\mode.o: ..\Function\Function.h
.\objects\mode.o: ..\HeaderFiles\HeaderFiles.h
.\objects\mode.o: ..\Fatfs\ff.h
.\objects\mode.o: ..\Fatfs\integer.h
.\objects\mode.o: ..\Fatfs\ffconf.h
.\objects\mode.o: ..\HardWare\KEY.h
.\objects\mode.o: ..\HardWare\LED\LED.h
.\objects\mode.o: ..\HardWare\USART0.h
.\objects\mode.o: ..\HardWare\tim.h
.\objects\mode.o: ..\HardWare\mode.h
.\objects\mode.o: ..\HardWare\ADC.h
.\objects\mode.o: ..\HardWare\oled.h
.\objects\mode.o: ..\HardWare\SPI_FLASH.h
.\objects\mode.o: ..\HardWare\RTC.h
.\objects\mode.o: ..\HardWare\sdcard.h
