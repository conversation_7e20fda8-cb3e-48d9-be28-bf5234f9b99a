.\objects\key.o: ..\HardWare\key.c
.\objects\key.o: ..\HardWare\key.h
.\objects\key.o: ..\HeaderFiles\HeaderFiles.h
.\objects\key.o: ..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\objects\key.o: ..\CMSIS\core_cm4.h
.\objects\key.o: D:\keilarm\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\key.o: ..\CMSIS\core_cmInstr.h
.\objects\key.o: ..\CMSIS\core_cmFunc.h
.\objects\key.o: ..\CMSIS\core_cm4_simd.h
.\objects\key.o: ..\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h
.\objects\key.o: ..\User\gd32f4xx_libopt.h
.\objects\key.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h
.\objects\key.o: ..\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\objects\key.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h
.\objects\key.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h
.\objects\key.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h
.\objects\key.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h
.\objects\key.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h
.\objects\key.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h
.\objects\key.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h
.\objects\key.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h
.\objects\key.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h
.\objects\key.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h
.\objects\key.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h
.\objects\key.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h
.\objects\key.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h
.\objects\key.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h
.\objects\key.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h
.\objects\key.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h
.\objects\key.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h
.\objects\key.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h
.\objects\key.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h
.\objects\key.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h
.\objects\key.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h
.\objects\key.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h
.\objects\key.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h
.\objects\key.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h
.\objects\key.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h
.\objects\key.o: D:\keilarm\ARM\ARMCC\Bin\..\include\stdlib.h
.\objects\key.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h
.\objects\key.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h
.\objects\key.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h
.\objects\key.o: ..\User\systick.h
.\objects\key.o: D:\keilarm\ARM\ARMCC\Bin\..\include\stdio.h
.\objects\key.o: D:\keilarm\ARM\ARMCC\Bin\..\include\string.h
.\objects\key.o: ..\Function\Function.h
.\objects\key.o: ..\Fatfs\ff.h
.\objects\key.o: ..\Fatfs\integer.h
.\objects\key.o: ..\Fatfs\ffconf.h
.\objects\key.o: ..\HardWare\KEY.h
.\objects\key.o: ..\HardWare\LED\LED.h
.\objects\key.o: ..\HeaderFiles\HeaderFiles.h
.\objects\key.o: ..\HardWare\USART0.h
.\objects\key.o: ..\HardWare\tim.h
.\objects\key.o: ..\HardWare\mode.h
.\objects\key.o: ..\HardWare\ADC.h
.\objects\key.o: ..\HardWare\oled.h
.\objects\key.o: ..\HardWare\SPI_FLASH.h
.\objects\key.o: ..\HardWare\RTC.h
.\objects\key.o: ..\HardWare\sdcard.h
